package com.smartPark.business;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.smartPark.BaseServiceApplication;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.job.entity.JobEntity;
import com.smartPark.common.job.mapper.JobEntityMapper;
import com.smartPark.common.job.service.JobSchedulerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

@RunWith(SpringRunner.class)
/**
 * 测试类基础注解
 * 增加webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
 * 作用：解决集成了websocket后测试类启动报javax.websocket.server.ServerContainer not available
 */
@SpringBootTest(classes = BaseServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
/** 与Application的启动类一致开始 **/
@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.smartPark.business.sanzhi.*")})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.smartPark"})
@ServletComponentScan
public class JobEntityReLoad {

    @Resource
    private JobSchedulerService jobSchedulerService;

    @Resource
    private CommonService commonService;

    @Resource
    private JobEntityMapper jobEntityMapper;

    //@Test
    public void reLoadFromDb() {
        //查询所有有效任务
        QueryWrapper<JobEntity> jobEntityQw = new QueryWrapper<>();
        jobEntityQw.eq("deleted_", CommonConstant.NOT_DELETE);

        List<JobEntity> jobEntityList = jobEntityMapper.selectList(jobEntityQw);
        if (CollectionUtil.isNotEmpty(jobEntityList)) {
            jobEntityList.forEach(jobEntity -> {
                //先删除任务
                checkJobEntity(jobEntity);
                jobSchedulerService.pause(jobEntity);
                //重新插入任务
                jobSchedulerService.resume(jobEntity);

                //更新任务修改信息
                commonService.setModifyInfo(jobEntity);
                jobEntityMapper.updateById(jobEntity);
            });
        }

    }

    /**
     * 校验jobEntity
     */
    private void checkJobEntity(JobEntity jobEntity) {
        Assert.notNull(jobEntity, "jobEntity 为空");
        Assert.hasLength(jobEntity.getJobName(), "jobName 为空");
        Assert.hasLength(jobEntity.getJobGroup(), "jobGroup 为空");
    }
}
