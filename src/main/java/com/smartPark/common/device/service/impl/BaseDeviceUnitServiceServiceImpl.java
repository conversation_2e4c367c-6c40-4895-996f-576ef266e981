package com.smartPark.common.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.device.dto.FieldSortDto;
import com.smartPark.common.device.mapper.BaseDeviceUnitServiceMapper;
import com.smartPark.common.device.service.BaseDeviceUnitServiceService;
import com.smartPark.common.device.service.DeviceVersionService;
import com.smartPark.common.entity.device.BaseDeviceUnitService;
import com.smartPark.common.entity.device.DeviceVersion;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
@Service
public class BaseDeviceUnitServiceServiceImpl extends ServiceImpl<BaseDeviceUnitServiceMapper, BaseDeviceUnitService> implements BaseDeviceUnitServiceService{

    @Autowired
    private DeviceVersionService deviceVersionService;

    @Override
    public List<FieldSortDto> getSort(Long deviceUnitId) {
        DeviceVersion deviceVersion = deviceVersionService.queryCurrentVersionByDeviceUnitId(deviceUnitId);
        if(deviceVersion == null){
            return null;
        }
        List<BaseDeviceUnitService> deviceUnitProperties = getBaseMapper().selectList(Wrappers.<BaseDeviceUnitService>lambdaQuery()
                .eq(BaseDeviceUnitService::getDeviceUnitId, deviceVersion.getDeviceUnitId())
                .eq(BaseDeviceUnitService::getDeviceVersionId, deviceVersion.getId())
        );
        return deviceUnitProperties == null ? null : deviceUnitProperties.stream().map(e->{
            FieldSortDto fieldSortDto = new FieldSortDto();
            fieldSortDto.setFieldName(e.getSerCode());
            fieldSortDto.setSort(e.getSort());
            return fieldSortDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getSort(Long deviceUnitId, String fieldName) {
        List<FieldSortDto> sort = getSort(deviceUnitId);
        if(sort == null){
            return null;
        }
        for (FieldSortDto fieldSortDto : sort) {
            if (fieldSortDto.getFieldName().equals(fieldName)) {
                return fieldSortDto.getSort();
            }
        }
        return null;
    }

    @Override
    public List<FieldSortDto> getShow(Long deviceUnitId) {
        DeviceVersion deviceVersion = deviceVersionService.queryCurrentVersionByDeviceUnitId(deviceUnitId);
        if(deviceVersion == null){
            return null;
        }
        List<BaseDeviceUnitService> deviceUnitProperties = getBaseMapper().selectList(Wrappers.<BaseDeviceUnitService>lambdaQuery()
                .eq(BaseDeviceUnitService::getDeviceUnitId, deviceVersion.getDeviceUnitId())
                .eq(BaseDeviceUnitService::getDeviceVersionId, deviceVersion.getId())
        );
        return deviceUnitProperties == null ? null : deviceUnitProperties.stream().map(e->{
            FieldSortDto fieldSortDto = new FieldSortDto();
            fieldSortDto.setFieldName(e.getSerCode());
            fieldSortDto.setSort(e.getShow());
            return fieldSortDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getShow(Long deviceUnitId, String fieldName) {
        List<FieldSortDto> sort = getShow(deviceUnitId);
        if(sort == null){
            return null;
        }
        for (FieldSortDto fieldSortDto : sort) {
            if (fieldSortDto.getFieldName().equals(fieldName)) {
                return fieldSortDto.getSort();
            }
        }
        return null;
    }

    @Override
    public void updatePropertySort(Long deviceUnitId, List<FieldSortDto> dto) {
        DeviceVersion deviceVersion = deviceVersionService.queryCurrentVersionByDeviceUnitId(deviceUnitId);
        if(deviceVersion == null){
            return ;
        }
        updatePropertySort(deviceVersion.getDeviceUnitId(),deviceVersion.getId(),dto);
    }

    @Override
    public void updatePropertyShow(Long deviceUnitId, List<FieldSortDto> dto) {

        DeviceVersion deviceVersion = deviceVersionService.queryCurrentVersionByDeviceUnitId(deviceUnitId);
        if(deviceVersion == null){
            return ;
        }

        for (FieldSortDto fieldSortDto : dto) {
            if(fieldSortDto.getSort() == null){
                continue;
            }
            BaseDeviceUnitService deviceUnitProperty = new BaseDeviceUnitService();
            deviceUnitProperty.setSort(fieldSortDto.getSort());
            getBaseMapper().update(deviceUnitProperty,
                    Wrappers.<BaseDeviceUnitService>lambdaUpdate()
                            .eq(BaseDeviceUnitService::getDeviceUnitId,deviceUnitId)
                            .eq(BaseDeviceUnitService::getDeviceVersionId,deviceVersion.getId())
                            .eq(BaseDeviceUnitService::getSerCode,fieldSortDto.getFieldName())
            );
        }
    }

    @Override
    public void updatePropertySort(Long deviceUnitId, Long deviceVersionId,List<FieldSortDto> dto) {

        for (FieldSortDto fieldSortDto : dto) {
            if(fieldSortDto.getSort() == null){
                continue;
            }
            BaseDeviceUnitService deviceUnitProperty = new BaseDeviceUnitService();
            deviceUnitProperty.setSort(fieldSortDto.getSort());
            getBaseMapper().update(deviceUnitProperty,
                    Wrappers.<BaseDeviceUnitService>lambdaUpdate()
                            .eq(BaseDeviceUnitService::getDeviceUnitId,deviceUnitId)
                            .eq(BaseDeviceUnitService::getDeviceVersionId,deviceVersionId)
                            .eq(BaseDeviceUnitService::getSerCode,fieldSortDto.getFieldName())
            );
        }

    }

    @Override
    public void updateService(DeviceVersion deviceVersion) {
        Long versionId = deviceVersion.getId();
        Long deviceUnitId = deviceVersion.getDeviceUnitId();
        String physicsModelStr = deviceVersion.getPhysicsModel();
        if(deviceUnitId ==null || versionId ==null || StringUtils.isBlank(physicsModelStr)){
            return;
        }
        JSONObject physicsModel = JSONObject.parseObject(physicsModelStr);
        List<BaseDeviceUnitService> addServices = new ArrayList<>();
        if(physicsModel!=null && CollectionUtil.isNotEmpty(physicsModel.getJSONArray("services"))){
            JSONArray servicesArray = physicsModel.getJSONArray("services");
            servicesArray.forEach(property->{
                JSONObject p = (JSONObject) property;
                String name = p.getString("name");
                String identifier = p.getString("identifier");
                JSONObject dataType = p.getJSONObject("dataType");
                String unit = dataType==null?null:dataType.getString("type");
                String specs = dataType==null?"{}":dataType.getString("specs");
                String inputs = dataType==null?null:dataType.getString("inputs");
                QueryWrapper<BaseDeviceUnitService> example = new QueryWrapper<>();
                example.eq("device_unit_id",deviceUnitId)
                        .eq("device_version_id",versionId)
                        .eq("ser_code",identifier);
                BaseDeviceUnitService existOne = getBaseMapper().selectOne(example);
                BaseDeviceUnitService deviceUnitService = new BaseDeviceUnitService();
                deviceUnitService.setDeviceUnitId(deviceUnitId);
                deviceUnitService.setDeviceVersionId(versionId);
                deviceUnitService.setSerCode(identifier);
                deviceUnitService.setSerName(name);
                deviceUnitService.setShow(existOne==null?1: existOne.getShow());
                deviceUnitService.setVersionDesc(deviceVersion.getDeviceVersionDesc());
                deviceUnitService.setUnit(unit);
                deviceUnitService.setSpecs(specs);
                deviceUnitService.setInputs(inputs);
                addServices.add(deviceUnitService);
            });
        }

        QueryWrapper<BaseDeviceUnitService> example = new QueryWrapper<>();
        example.eq("device_unit_id",deviceUnitId)
                .eq("device_version_id",versionId);
        getBaseMapper().delete(example);

        if(CollectionUtil.isNotEmpty(addServices)){
            for (BaseDeviceUnitService addSer : addServices) {
                getBaseMapper().insert(addSer);
            }
        }
    }
}
