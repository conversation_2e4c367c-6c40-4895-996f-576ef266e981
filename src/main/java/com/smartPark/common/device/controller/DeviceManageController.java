package com.smartPark.common.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.device.dto.ApiDownResultDTO;
import com.smartPark.common.device.dto.DeviceManageDto;
import com.smartPark.common.device.model.PhysicsModel;
import com.smartPark.common.device.model.PhysicsService;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.service.IDeviceManageService;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.device.vo.DeviceManageVo;
import com.smartPark.common.entity.device.dto.DeviceObjInfoDevicePropertyStatusListInfo;
import com.smartPark.common.security.context.BaseUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @description: 设备管理
 * @author: Yan XinYu
 */
@Slf4j
@RestController
@RequestMapping("/deviceManage")
public class DeviceManageController {

    @Resource
    private BaseUserContextProducer baseUserContextProducer;

    @Autowired
    private IDeviceManageService deviceManageService;

    @Autowired
    private DeviceService deviceService;

    @PostMapping("/selectPage")
    public RestMessage selectPage(@RequestBody RequestModel<DeviceManageDto> requestModel) {
        IPage<DeviceManageVo> result = deviceManageService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(result).build();
    }

    @DeleteMapping("/delete")
    public RestMessage delete(@RequestParam("deviceCode") String deviceCode){
        deviceManageService.del(deviceCode);
        return RestBuilders.successBuilder().build();
    }
    /**
     * 批量删除
     * @param deviceCodes
     * @return
     */
    @DeleteMapping
    public RestMessage delBatch(@RequestBody List<String> deviceCodes){
        deviceCodes.forEach((deviceCode)-> deviceManageService.del(deviceCode));
        return RestBuilders.successBuilder().build();
    }

    @GetMapping("/deviceProp")
    public RestMessage deviceProp(@RequestParam("deviceCode") String deviceCode){
        // List<DeviceUnitPropertyDto> result = deviceManageService.queryPropertyWithName(deviceCode);
        // return RestBuilders.successBuilder(result).build();
        //设备详情
        DeviceObjInfoDevicePropertyStatusListInfo deviceObjInfoDevicePropertyStatusListInfo = new DeviceObjInfoDevicePropertyStatusListInfo();

        //update 2023 05 23
        DeviceUtils.setDeviceDetail(deviceObjInfoDevicePropertyStatusListInfo,deviceCode);
        return RestBuilders.successBuilder(deviceObjInfoDevicePropertyStatusListInfo).build();
    }

    @PostMapping("/importFile")
    public RestMessage importFile(@RequestParam("file") MultipartFile file) throws IOException {
        Long userId = baseUserContextProducer.getCurrent().getId();
        Long taskId = deviceManageService.importDevice(userId,file.getInputStream());
        return RestBuilders.successBuilder(taskId).build();
    }

    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {

        DeviceManageVo deviceManageVo = new DeviceManageVo();
        deviceManageVo.setBsm("6147B1283344");
        deviceManageVo.setDeviceId("6147B1283344");
        deviceManageVo.setObjId("6147B1283344");
        deviceManageVo.setSbmc("电表设备001");
        deviceManageVo.setSbxh("Test-123-23-12");
        deviceManageVo.setSzjd("洪山街");
        deviceManageVo.setSzsq("软件园社区");
        deviceManageVo.setAreaPath("二单元");

        deviceManageService.download("设备导入模版.xlsx", Collections.singletonList(deviceManageVo),response);
    }

    @PostMapping("/download")
    public RestMessage download(@RequestBody DeviceManageDto requestModel, HttpServletResponse response) throws IOException {
        Long userId = baseUserContextProducer.getCurrent().getId();
        Long taskId = deviceManageService.asyncDownload(userId,"设备导出",requestModel);
        return RestBuilders.successBuilder(taskId).build();
    }

//    @GetMapping("/propList")
//    public RestMessage propList(@RequestParam("deviceCode") String deviceCode){
//        List<PhysicsService> physicsServices = deviceManageService.queryCurrentPhysicsService(deviceCode);
//        return RestBuilders.successBuilder(physicsServices).build();
//    }

    @GetMapping("/serviceList")
    public RestMessage serviceList(@RequestParam("deviceCode") String deviceCode){
        List<PhysicsService> physicsServices = deviceManageService.queryCurrentPhysicsService(deviceCode);
        return RestBuilders.successBuilder(physicsServices).build();
    }

    @GetMapping("/getPhysicsModel")
    public RestMessage getPhysicsModel(@RequestParam("deviceCode") String deviceCode){
        PhysicsModel physicsModel = deviceManageService.queryCurrentPhysicsModel(deviceCode);
        return RestBuilders.successBuilder(physicsModel).build();
    }

    @PostMapping("/send")
    public RestMessage send(@RequestBody Map<String, Object> downParameter){
        ApiDownResultDTO apiDownResultDTO = deviceService.send(downParameter);
        return RestBuilders.successBuilder(apiDownResultDTO).build();
    }

}
