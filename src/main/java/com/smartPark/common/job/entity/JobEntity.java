package com.smartPark.common.job.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务实体类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_job_entity")
public class JobEntity extends Model<JobEntity> {

    /**
     * id 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键", example = "")
    private Integer id;

    /**
     * 任务名称
     */
    @TableField("job_name")
    @ApiModelProperty(value = "任务名称", example = "")
    private String jobName;

    /**
     * 任务分组
     */
    @TableField("job_group")
    @ApiModelProperty(value = "任务分组", example = "")
    private String jobGroup;

    /**
     * 任务状态， 0禁用 1启用
     */
    @TableField("job_status")
    @ApiModelProperty(value = "任务状态", example = "")
    private Boolean jobStatus;

    /**
     * 任务cron表达式
     */
    @TableField("cron_expression")
    @ApiModelProperty(value = "任务cron表达式", example = "")
    private String cronExpression;

    /**
     * 任务描述
     */
    @TableField("remark")
    @ApiModelProperty(value = "任务描述", example = "")
    private String remark;

    /**
     * 任务调度类型，0：方法，1：直接调用http接口
     */
    @TableField("job_task_type")
    @ApiModelProperty(value = "任务调度类型", example = "")
    private Integer jobTaskType;


    /**
     * 应用id
     */
    @TableField("application_id")
    private Long applicationId;

    /**
     * 参数
     */
    @TableField("params")
    @ApiModelProperty(value = "参数", example = "")
    private String params;

    /**
     * 任务类型，0：周期性，1：一次性
     */
    @TableField("job_type")
    @ApiModelProperty(value = "任务类型", example = "")
    private Integer jobType;


    /**
     * 是否立即运行，0：否，1：是
     */
    @TableField("want_now_run")
    @ApiModelProperty(value = "是否立即运行", example = "0")
    private Boolean wantNowRun;

    /**
     * 生效日期
     */
    @TableField("start_date")
    @ApiModelProperty(value = "生效日期", example = "")
    private Date startDate;

    /**
     * 失效日期
     */
    @TableField("end_date")
    @ApiModelProperty(value = "失效日期", example = "")
    private Date endDate;

    /**
     * 执行类名
     */
    @TableField("job_class_name")
    @ApiModelProperty(value = "执行类名", example = "")
    private String jobClassName = "com.smartPark.common.job.service.impl.JobExecutorServiceImpl";

    /**
     * 执行任务方法名称
     */
    @TableField("function_path")
    @ApiModelProperty(value = "执行方法名称", example = "")
    private String functionPath;

    /**
     * 任务创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "任务创建时间", example = "")
    private Date createTime;

    /**
     * 任务更新时间
     */
    @TableField("modify_time")
    @ApiModelProperty(value = "任务更新时间", example = "")
    private Date modifyTime;


    /**
     * 是否已经添加到调度过一次
     */
    @TableField("had_scheduled_once")
    private Boolean hadScheduledOnce;

    /**
     * 是否删除字段 0:未删除; 其他：删除
     */
    @TableLogic(value = "0")
    @TableField("deleted_")
    private Integer deleted = 0;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private Long creatorId;


    /**
     * 修改人id
     */
    @TableField("modify_id")
    private Long modifyId;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public JobEntity() {
    }

    public JobEntity(String jobName, String jobGroup) {
        this.jobName = jobName;
        this.jobGroup = jobGroup;
    }
}
