package com.smartPark.common.job;

import com.smartPark.common.cronHelp.JobGroupConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 任务实体类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@Builder
@AllArgsConstructor
public class JobEntity{

    /**
     * id 主键
     */
    private Integer id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务分组
     */
    private String jobGroup;

    /**
     * 任务状态， 0禁用 1启用
     */
    private Boolean jobStatus;

    /**
     * 任务cron表达式
     */
    private String cronExpression;

    /**
     * 任务描述
     */
    private String remark;

    /**
     * 任务调度类型，0：接口，1：直接调用http接口
     */
    private Integer jobTaskType;


    /**
     * 应用id
     */
    private Long applicationId;

    /**
     * 参数
     */
    private String params;

    /**
     * 任务类型，0：周期性，1：一次性
     */
    private Integer jobType;


    /**
     * 是否立即运行，0：否，1：是
     */
    private Boolean wantNowRun;

    /**
     * 生效日期
     */
    private Date startDate;

    /**
     * 失效日期
     */
    private Date endDate;

    /**
     * 执行类名
     */
    private String jobClassName = "com.smartPark.common.job.service.impl.JobExecutorServiceImpl";

    /**
     * 执行任务方法名称
     */
    private String functionPath;

    /**
     * 任务创建时间
     */
    private Date createTime;

    /**
     * 任务更新时间
     */
    private Date modifyTime;


    /**
     * 是否已经添加到调度过一次
     */
    private Boolean hadScheduledOnce;

    /**
     * 是否删除字段 0:未删除; 其他：删除
     */
    private Integer deleted = 0;

    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 修改人id
     */
    private Long modifyId;

    public JobEntity() {
    }

    /**
     * 根据定义JobGroupConstant 生成job实体
     */
    public static JobEntity getJobByJobGroup(JobGroupConstant.ModelGroupNameEnum modelGroupNameEnum){
        JobEntity jobEntity = new JobEntity();
        String jobName = modelGroupNameEnum.getApplicationId() + "_" + modelGroupNameEnum.getModelId() + "_" + modelGroupNameEnum.getJobName();
        String jobGroup = modelGroupNameEnum.getApplicationId() + "_" + modelGroupNameEnum.getModelId() + "_" + modelGroupNameEnum.getJobGroup();
        jobEntity.setJobName(jobName);
        jobEntity.setJobGroup(jobGroup);
        jobEntity.setApplicationId(modelGroupNameEnum.getApplicationId());
        return jobEntity;
    }
}
