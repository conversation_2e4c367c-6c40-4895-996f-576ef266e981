package com.smartPark.common.async;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringUtils implements ApplicationContextAware {

  private static Logger LOG = LoggerFactory.getLogger(SpringUtils.class);

  private static ApplicationContext applicationContext;

  @Override
  public void setApplicationContext(ApplicationContext applicationContext)
      throws BeansException {
    if (SpringUtils.applicationContext == null) {
      SpringUtils.applicationContext = applicationContext;
    }
    LOG.info(
        "---------------------------------------------------------------------");

    LOG.info(
        "--------------- com.server.edu.dictionary.utils.SpringUtils  --------");

    LOG.info(
        "---------------------------------------------------------------------");
  }

  //获取applicationContext
  public static ApplicationContext getApplicationContext() {
    if (applicationContext == null) {
      LOG.error("---------------------------------------------");
      LOG.error("SpringUtils applicationContext is null");
      LOG.error("Add @Import(SpringUtils.class) On Starter Class");
      LOG.error("---------------------------------------------");
    }
    return applicationContext;
  }

  //通过name获取 Bean.
  public static Object getBean(String name) {
    return getApplicationContext().getBean(name);
  }

  //通过class获取Bean.
  public static <T> T getBean(Class<T> clazz) {
    return getApplicationContext().getBean(clazz);
  }

  //通过name,以及Clazz返回指定的Bean
  public static <T> T getBean(String name, Class<T> clazz) {
    return getApplicationContext().getBean(name, clazz);
  }

}