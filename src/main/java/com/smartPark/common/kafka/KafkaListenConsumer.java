package com.smartPark.common.kafka;


import com.alibaba.fastjson.JSON;
import com.smartPark.business.ruleEngine.service.RuleEngineService;
import com.smartPark.common.alarm.entity.Alarm;
import com.smartPark.common.alarm.service.AlarmService;
import com.smartPark.common.constant.KafkaTopic;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.flowDataFacade.FlowDataFacadeInterface;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * kafka消费者listener
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KafkaListenConsumer {

//    @Resource
//    private ToiletDeviceService toiletDeviceService;

    @Resource
    private RuleEngineService ruleEngineService;

    @KafkaListener(topics = KafkaTopic.ALARM_MESSAFE_TOPIC)
    public void deviceFlowListen(List<ConsumerRecord> records, Acknowledgment ack) {
        log.debug("=====管理后台告警消息alarmMessageListen消费者接收信息====");
        try {
            for (ConsumerRecord record : records) {
                log.debug("---开启线程解析告警消息数据:{}", record.toString());
                Optional<?> kafkaMessage = Optional.ofNullable(record.value());
                if (kafkaMessage.isPresent()) {
                    Object data = kafkaMessage.get();
                    if (StringUtils.isNotBlank(String.valueOf(data))) {
                        String recordStr = String.valueOf(data);
                        Alarm alarm = JSON.parseObject(recordStr, Alarm.class);
                        if (alarm != null) {
                            ruleEngineService.sendMessageNotice(alarm);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("----告警消息消费者解析数据异常:{}", e.getMessage(), e);
        } finally {
            //手动提交偏移量
            ack.acknowledge();
        }
    }
}
