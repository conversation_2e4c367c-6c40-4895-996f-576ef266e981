package com.smartPark.common.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 告警处理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_alarm_dispose")
public class AlarmDispose extends BaseEntity<AlarmDispose> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 告警ID（关联告警表主键）
     */
    @TableField("alarm_id_")
    private Long alarmId;

    /**
     * 处理时间
     */
    @TableField("dispose_time_")
    private Date disposeTime;

    /**
     * 处理内容
     */
    @TableField("dispose_content_")
    private String disposeContent;

    /**
     * 处理状态 处理状态：1未处理，2已处理，3处理中
     */
    @TableField("dispose_status_")
    private Integer disposeStatus;

    /**
     * 处理图片URL，多个图片用逗号分隔
     */
    @TableField("dispose_images_")
    private String disposeImages;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}