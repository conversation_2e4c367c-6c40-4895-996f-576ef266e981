package com.smartPark.business.electronicfence.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.electronicfence.entity.dto.CarAlarmDTO;
import com.smartPark.business.electronicfence.service.ICarAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;

/**
 * PositionDevice管理
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
@Slf4j
@RestController
@RequestMapping("carAlarm")
public class CarAlarmController extends ApiController {

    @Resource
    private ICarAlarmService service;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<CarAlarmDTO> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<CarAlarmDTO> record = service.queryPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }


    /**
     * 根据id查询单条设备告警详情
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id 不能为空");
        CarAlarmDTO alarmDTO = service.findById(id);
        return RestBuilders.successBuilder().data(alarmDTO).build();
    }

}

