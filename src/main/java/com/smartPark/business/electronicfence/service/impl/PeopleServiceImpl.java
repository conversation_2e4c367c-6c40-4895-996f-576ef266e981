package com.smartPark.business.electronicfence.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.smartPark.business.electronicfence.entity.People;
import com.smartPark.business.electronicfence.entity.dto.PeopleQueryDTO;
import com.smartPark.business.electronicfence.entity.dto.WorkerDTO;
import com.smartPark.business.electronicfence.mapper.PeopleMapper;
import com.smartPark.business.electronicfence.service.PeopleService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.device.dto.DeviceStatusDTO;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.security.entity.Worker;
import com.smartPark.common.security.mapper.WorkerMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;

/**
 * <p>
 * 人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Service
public class PeopleServiceImpl extends ServiceImpl<PeopleMapper, People> implements PeopleService {

  @Resource
  private PeopleMapper peopleMapper;

  @Resource
  private WorkerMapper workerMapper;

  @Resource
  private DeviceService deviceService;

  @Resource
  private CommonService commonService;

  @Resource
  private DeviceStatusMapper deviceStatusMapper;

  @Override
  public IPage<Worker> selectUnrelatedPage(Page page, PeopleQueryDTO customQueryParams) {
    // 查询所有的人员电话
    List<String> mobiles = peopleMapper.selectMobiles();
    customQueryParams.setMobiles(mobiles);
    LambdaQueryWrapper<Worker> queryWrapper = new LambdaQueryWrapper<>();
    if(CollectionUtil.isNotEmpty(mobiles)){
      queryWrapper.notIn(Worker::getMobile, mobiles)
          .like(Worker::getName, customQueryParams.getName())
          .orderByDesc(Worker::getCreateTime);
    }else{
      queryWrapper.like(Worker::getName, customQueryParams.getName())
          .orderByDesc(Worker::getCreateTime);
    }
    // 查询所有的人员
    IPage<Worker> workers = workerMapper.selectPage(page, queryWrapper);
    return workers;
  }

  @Override
  public IPage<Worker> selectPage(Page page, PeopleQueryDTO customQueryParams) {
    // 查询所有的人员电话
//    List<String> mobiles = peopleMapper.selectMobiles();
//    if(CollectionUtil.isEmpty(mobiles)){
//      return null;
//    }
//    customQueryParams.setMobiles(mobiles);
//    LambdaQueryWrapper<Worker> queryWrapper = new LambdaQueryWrapper<>();
//    queryWrapper.in(Worker::getMobile, mobiles)
//        .like(Worker::getName, customQueryParams.getName())
//        .orderByDesc(Worker::getCreateTime);
    // 查询所有的人员
    IPage<Worker> workers = peopleMapper.findPage(page, customQueryParams);
    return workers;
  }

  @Override
  public void delBatch(List<String> mobiles) {
    StringJoiner sj = new StringJoiner("，");
    mobiles.forEach(sj::add);
    LambdaQueryWrapper<People> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(People::getMobile, mobiles);
    peopleMapper.delete(queryWrapper);
    LogHelper.setLogInfo("", mobiles.toString(), null, null,"删除人员，手机号："+sj);
  }

  @Override
  public WorkerDTO findByMobile(String mobile) {
    LambdaQueryWrapper<People> peopleLambdaQueryWrapper = new LambdaQueryWrapper<>();
    peopleLambdaQueryWrapper.eq(People::getMobile, mobile);
    People people = peopleMapper.selectOne(peopleLambdaQueryWrapper);
    if(people != null){
      WorkerDTO workerDTO = new WorkerDTO();
      LambdaQueryWrapper<Worker> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(Worker::getMobile, people.getMobile());
      Worker worker = workerMapper.selectOne(queryWrapper);
      BeanUtils.copyProperties(worker, workerDTO);
      // 查询设备信息
      String deviceCode = workerDTO.getDeviceBind();
      // 用optional判断空deviceCode
      Device deviceByCode = Optional.ofNullable(deviceService.getDeviceByCode(deviceCode)).orElse(new Device());
      workerDTO.setDevice(deviceByCode);
      // 查询设备属性列表
      workerDTO.setDevicePropertyStatusList(getDevicePropertyStatus(new DeviceStatusDTO().setDeviceCodes(
          Lists.newArrayList(deviceCode))));
      return workerDTO;
    }
    return null;
  }

  @Override
  public void insert(List<String> mobiles) {
    StringJoiner sj = new StringJoiner("，");
    for (String mobile : mobiles) {
      // 查询是否已经存在
      LambdaQueryWrapper<People> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(People::getMobile, mobile);
      People one = peopleMapper.selectOne(queryWrapper);
      if(one != null){
        continue;
      }
      People people = new People();
      commonService.setCreateAndModifyInfo(people);
      people.setMobile(mobile);
      peopleMapper.insert(people);
      sj.add(mobile);
    }
    LogHelper.setLogInfo("", mobiles.toString(), null, null,"新增人员，手机号："+sj);

  }

  /**
   * 设置设备属性
   *
   * @param deviceStatusDTO
   * @return
   */
  private List<DevicePropertyStatus> getDevicePropertyStatus(DeviceStatusDTO deviceStatusDTO) {
    List<DevicePropertyStatus> devicePropertyStatusList = deviceStatusMapper.listVisiblePropertyStatus(deviceStatusDTO);
    devicePropertyStatusList.forEach((item) -> {
      if (item.getUnit().equals("enum") || "bool".equals(item.getUnit())) {
        JSONObject specsMap = item.getSpecsMap();
        if (specsMap != null && specsMap.containsKey(item.getValue())) {
          item.setValue(specsMap.get(item.getValue()).toString());
        }
      }
    });
    return devicePropertyStatusList;
  }
}
