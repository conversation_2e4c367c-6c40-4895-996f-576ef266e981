package com.smartPark.business.electronicfence.entity.dto;

import cn.hutool.core.util.IdUtil;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;

/**
 * @Description 告警统计
 * <AUTHOR>
 * @Date 2023/9/19 11:15
 */
@Data
public class AlarmCountDTO implements TransPojo {
    /**
     * 翻译框架必须要id 并且id唯一
     */
    private String id;

    public String getId() {
        return IdUtil.simpleUUID();
    }

    /**
     * 告警类型
     */
    @Trans(type= TransType.DICTIONARY,key = "allEventType",ref = "alarmName")
    private String alarmType;

    /**
     * 告警类型
     */
    private String alarmName;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 次数
     */
    private Integer num;
}
