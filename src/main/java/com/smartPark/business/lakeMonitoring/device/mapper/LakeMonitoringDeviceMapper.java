package com.smartPark.business.lakeMonitoring.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.lakeMonitoring.device.entity.LakeMonitoringDevice;
import com.smartPark.business.lakeMonitoring.device.entity.vo.LakeMonitoringDeviceVo;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 湖渠监测设备表 Mapper 接口
 * </p>

 * <AUTHOR> @since
 */
public interface LakeMonitoringDeviceMapper extends BaseMapper<LakeMonitoringDevice> {

    IPage<LakeMonitoringDeviceVo> queryListByPage(@Param("page") Page page, @Param("lakeMonitoringDeviceVo") LakeMonitoringDeviceVo lakeMonitoringDeviceVo);

    List<DeviceArea> getAreas();

    List<LakeMonitoringDevice> selectOnline();

}
