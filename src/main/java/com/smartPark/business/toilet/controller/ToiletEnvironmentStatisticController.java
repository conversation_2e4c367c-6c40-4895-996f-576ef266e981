package com.smartPark.business.toilet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.toilet.entity.LivableToiletFlowRecords;
import com.smartPark.business.toilet.entity.LivableToiletStageStandardStat;
import com.smartPark.business.toilet.entity.vo.*;
import com.smartPark.business.toilet.service.LivableToiletStageStandardStatService;
import com.smartPark.business.toilet.service.ToiletEnvironmentStatisticService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 智慧公厕/统计分析/环境数据
 * @author: Yan XinYu
 */
@RequestMapping("toiletEnvironmentStatistic")
@RestController
@Api(tags = "环境数据")
public class ToiletEnvironmentStatisticController {

    @Autowired
    private ToiletEnvironmentStatisticService toiletEnvironmentStatisticService;

    @Autowired
    private LivableToiletStageStandardStatService toiletStageStandardStatService;

    /**
     * 汇总统计
     */
    @GetMapping("/stat")
    @ApiOperation("汇总统计")
    public RestMessage stat() {
        ToiletEnvironmentStat stat = toiletEnvironmentStatisticService.stat();
        return RestBuilders.successBuilder().data(stat).build();
    }

    /**
     * 客流量：折线图统计
     */
    @PostMapping("/env/lineChart")
    @ApiOperation("环境趋势折线图统计")
    public RestMessage lineChart(@RequestBody QueryLineChartVo lineChartVo) {
        List<ToiletEnvironmentStatusVo> toiletEnvironmentStatusVos = toiletEnvironmentStatisticService.lineChat(lineChartVo);
        return RestBuilders.successBuilder().data(toiletEnvironmentStatusVos).build();
    }

    /**
     * 达标分析：折线图统计
     */
    @PostMapping("/env/standard/lineChart")
    @ApiOperation("达标分析折线图统计")
    public RestMessage standardLineChart(@RequestBody QueryLineChartVo lineChartVo) {
        List<ToiletFlowTimeStatusVo> toiletFlowTimeStatusVos = toiletStageStandardStatService.lineChart(lineChartVo);
        return RestBuilders.successBuilder().data(toiletFlowTimeStatusVos).build();
    }

    /**
     * 达标明细
     */
    @PostMapping("/env/standard/queryPage")
    @ApiOperation("达标明细表")
    public RestMessage lineChart(@RequestBody RequestModel<QueryLineChartVo> model) {
        IPage<LivableToiletStageStandardStat> result = toiletStageStandardStatService.queryPage(model);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * 达标统计
     */
    @PostMapping("/env/standard/queryStatByDate")
    @ApiOperation("达标统计")
    public RestMessage queryStatByDate(@RequestBody QueryLineChartVo vo) {
        Map<String, ToiletStageStandardThreeLayerStat> result = toiletStageStandardStatService.queryStatByDate(vo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * 导出 excel
     */
    @PostMapping("/env/standard/exportExcel")
    public RestMessage exportExcel(@RequestBody QueryLineChartVo vo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String taskKey = toiletStageStandardStatService.exportExcel(vo,request,response);
        return RestBuilders.successBuilder().data(taskKey).build();
    }


}
