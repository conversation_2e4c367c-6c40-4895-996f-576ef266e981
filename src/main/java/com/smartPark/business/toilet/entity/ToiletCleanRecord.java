package com.smartPark.business.toilet.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 公厕清洁记录
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_toilet_clean_record")
public class ToiletCleanRecord extends Model<ToiletCleanRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 清洁时间
     */
    @TableField("clean_time_")
    private LocalDateTime cleanTime;

    /**
     * 公厕id
     */
    @TableField("toilet_id_")
    private Long toiletId;

    /**
     * 保洁人员id
     */
    @TableField("clean_person_id_")
    private Long cleanPersonId;

    /**
     * 清洁内容
     */
    @TableField("clean_content")
    private String cleanContent;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private LocalDateTime modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;


}
