package com.smartPark.business.toilet.entity.dto;

import com.smartPark.business.toilet.entity.ToiletCleanAtte;
import com.smartPark.business.toilet.entity.ToiletCleanPerson;
import com.smartPark.business.toilet.entity.ToiletInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ToiletCleanAtte实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/24
 */

@Data
@Accessors(chain = true)
public class ToiletCleanAtteDTO extends ToiletCleanAtte {
    /**
     * 公厕信息
     */
    private ToiletInfo toiletInfo;

    /**
     * 保洁人员信息
     */
    private ToiletCleanPerson toiletCleanPerson;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
