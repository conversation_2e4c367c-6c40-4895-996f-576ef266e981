package com.smartPark.business.toilet.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.toilet.entity.ToiletInfo;
import com.smartPark.business.toilet.entity.vo.*;
import com.smartPark.business.toilet.mapper.LivableToiletEnvStatMapper;
import com.smartPark.business.toilet.mapper.ToiletInfoMapper;
import com.smartPark.business.toilet.service.*;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Yan XinYu
 */
@Service
public class ToiletEnvironmentStatisticServiceImpl implements ToiletEnvironmentStatisticService {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private LivableToiletEnvStatMapper toiletEnvStatMapper;

    @Autowired
    private LivableToiletStageStandardStatService toiletStageStandardStatService;

    @Autowired
    private ToiletMapService toiletMapService;

    @Autowired
    private ToiletInfoMapper toiletInfoMapper;

    @Override
    public ToiletEnvironmentStat stat() {
        ToiletEnvironmentStatusVo toiletEnvironmentStatusVo = latestDate(LocalDate.now().toString());
        ToiletEnvironmentStat stat = new ToiletEnvironmentStat();
        BeanUtil.copyProperties(toiletEnvironmentStatusVo,stat);

        stat.setWeekStandardNum(toiletStageStandardStatService.weekStandardNum());
        stat.setLastWeekStandardNum(toiletStageStandardStatService.lastWeekStandardNum());
        stat.setWeekCycleRatio(LivableToiletFlowRecordsService.cycleRatio(stat.getLastWeekStandardNum(),stat.getWeekStandardNum()));
        stat.setMonthStandardNum(toiletStageStandardStatService.monthStandardNum());
        stat.setLastMonthStandardNum(toiletStageStandardStatService.lastMonthStandardNum());
        stat.setMonthCycleRatio(LivableToiletFlowRecordsService.cycleRatio(stat.getLastMonthStandardNum(),stat.getMonthStandardNum()));
        stat.setYearStandardNum(toiletStageStandardStatService.yearStandardNum());
        stat.setLastYearStandardNum(toiletStageStandardStatService.lastYearStandardNum());
        stat.setYearCycleRatio(LivableToiletFlowRecordsService.cycleRatio(stat.getLastYearStandardNum(),stat.getYearStandardNum()));
        return stat;
    }

    @Override
    public List<ToiletEnvironmentStatusVo> lineChat(QueryLineChartVo lineChartVo) {
        //公厕总数
        ToiletInfoVo toiletInfoVo = new ToiletInfoVo();
        ObjInfo objInfo = new ObjInfo();
        objInfo.setSzjd(lineChartVo.getSzjd());
        objInfo.setSzsq(lineChartVo.getSzsq());
        objInfo.setSzdywg(lineChartVo.getSzdywg());
        toiletInfoVo.setObjInfo(objInfo);
        List<String> toiletCodes = toiletInfoMapper.selectDtoPage(new Page<>(1, -1), toiletInfoVo)
                .getRecords().stream().map(ToiletInfo::getToiletCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(toiletCodes)){
            toiletCodes.add("-1");
        }
        switch (lineChartVo.getTimeType()){
            case 0:
                return timeStats(toiletCodes,LocalDate.now().toString());
            case 1:
                return dateStats(toiletCodes,lineChartVo.getStartDate(),lineChartVo.getEndDate());
            case 2:
                return monthStats(toiletCodes,lineChartVo.getStartDate(),lineChartVo.getEndDate());
            case 3:
                return yearStats(toiletCodes,lineChartVo.getStartDate(),lineChartVo.getEndDate());
            default:
                throw new BusinessException("未识别时间类型:" + lineChartVo.getTimeType());
        }
    }

    @Override
    public List<ToiletEnvironmentStatusVo> timeStats(List<String> toiletCodes,String date) {
        Map<String, ToiletEnvironmentStatusVo> collect = toiletEnvStatMapper.timeStats(toiletCodes, date)
                .stream()
                .collect(Collectors.toMap(ToiletEnvironmentStatusVo::getTime, a -> a, (k1, k2) -> k1));
        List<ToiletEnvironmentStatusVo> result = new ArrayList<>(24);
        for (int i = 0; i < 24; i++) {
            ToiletEnvironmentStatusVo sub = collect.get(String.valueOf(i));
            if(sub == null){
                sub = new ToiletEnvironmentStatusVo();
                sub.setTime(String.valueOf(i));
            }
            result.add(sub);
        }
        return result;
    }

    @Override
    public ToiletEnvironmentStatusVo latestDate(String date){
        MonitorQueryVO queryVO = new MonitorQueryVO();
        queryVO.setDeviceUnitCode("CSKQ-4G-01-0185");
        queryVO.setFieldList(ListUtil.toList("deviceCode","nh3_concentration","h2s_concentration","humidity","temperature"));
        queryVO.setQueryTimeStart(DateUtil.format(DateUtil.parseDate(date), DatePattern.NORM_DATETIME_PATTERN));
        queryVO.setQueryTimeEnd(DateUtil.format(DateUtil.parseDate(date).offset(DateField.DAY_OF_MONTH,1), DatePattern.NORM_DATETIME_PATTERN));

        List<Map<String, Object>> records = monitorService.queryRecordsFromEsByParams(new RequestModel<>(new Page<>(1,1), queryVO));
        List<ToiletMonitorInfo> toiletMonitorInfos = BeanUtil.copyToList(records, ToiletMonitorInfo.class);
        ToiletEnvironmentStatusVo vo = new ToiletEnvironmentStatusVo();

        for (ToiletMonitorInfo toiletMonitorInfo : toiletMonitorInfos) {
            vo.maxNh3Concentration(toiletMonitorInfo.getNh3Concentration());
            vo.maxH2sConcentration(toiletMonitorInfo.getH2sConcentration());
            vo.maxTemperature(toiletMonitorInfo.getTemperature());
            vo.maxHumidity(toiletMonitorInfo.getHumidity());
        }
        return vo;
    }

    @Override
    public List<ToiletEnvironmentStatusVo> dateStats(List<String> toiletCodes, String startDate, String endDate) {
        Map<String, ToiletEnvironmentStatusVo> collect = toiletEnvStatMapper.dateStats(toiletCodes,
                DateUtil.format(DateUtil.parse(startDate),DatePattern.NORM_DATE_PATTERN),
                DateUtil.format(DateUtil.parse(endDate),DatePattern.NORM_DATE_PATTERN))
                .stream()
                .collect(Collectors.toMap(ToiletEnvironmentStatusVo::getTime, a -> a, (k1, k2) -> k1));
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(startDate,DatePattern.NORM_DATE_PATTERN),
                DateUtil.parse(endDate,DatePattern.NORM_DATE_PATTERN), DateField.DAY_OF_MONTH);
        List<ToiletEnvironmentStatusVo> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            String format = DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN);
            result.add(collect.getOrDefault(format,new ToiletEnvironmentStatusVo(format)));
        }
        return result;
    }

    @Override
    public List<ToiletEnvironmentStatusVo> monthStats(List<String> toiletCodes, String startMonth, String endMonth) {
        Map<String, ToiletEnvironmentStatusVo> collect = toiletEnvStatMapper.monthStats(toiletCodes,
                DateUtil.format(DateUtil.parse(startMonth),DatePattern.NORM_MONTH_PATTERN),
                DateUtil.format(DateUtil.parse(endMonth),DatePattern.NORM_MONTH_PATTERN))
                .stream()
                .collect(Collectors.toMap(ToiletEnvironmentStatusVo::getTime, a -> a, (k1, k2) -> k1));
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(startMonth,DatePattern.NORM_MONTH_PATTERN), DateUtil.parse(endMonth,DatePattern.NORM_MONTH_PATTERN), DateField.MONTH);
        List<ToiletEnvironmentStatusVo> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            String format = DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN);
            result.add(collect.getOrDefault(format,new ToiletEnvironmentStatusVo(format)));
        }
        return result;
    }

    @Override
    public List<ToiletEnvironmentStatusVo> yearStats(List<String> toiletCodes, String startYear, String endYear) {
        Map<String, ToiletEnvironmentStatusVo> collect = toiletEnvStatMapper.yearStats(toiletCodes,
                        DateUtil.format(DateUtil.parse(startYear),DatePattern.NORM_YEAR_PATTERN),
                        DateUtil.format(DateUtil.parse(endYear),DatePattern.NORM_YEAR_PATTERN))
                .stream()
                .collect(Collectors.toMap(ToiletEnvironmentStatusVo::getTime, a -> a, (k1, k2) -> k1));
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parse(startYear,DatePattern.NORM_YEAR_PATTERN), DateUtil.parse(endYear,DatePattern.NORM_YEAR_PATTERN), DateField.YEAR);
        List<ToiletEnvironmentStatusVo> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            String format = DateUtil.format(dateTime, DatePattern.NORM_YEAR_PATTERN);
            result.add(collect.getOrDefault(format,new ToiletEnvironmentStatusVo(format)));
        }
        return result;
    }
}
