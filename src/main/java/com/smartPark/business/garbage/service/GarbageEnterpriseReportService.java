package com.smartPark.business.garbage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.garbage.entity.GarbageEnterpriseReport;
import com.smartPark.business.garbage.entity.vo.GarbageEnterpriseReportVo;
import com.smartPark.common.base.model.RequestModel;

/**
 * <p>
 * 企业申报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface GarbageEnterpriseReportService extends IService<GarbageEnterpriseReport> {

  IPage<GarbageEnterpriseReport> queryListByPage(RequestModel<GarbageEnterpriseReport> requestModel);

  /**
   * 增加
   * @param garbageEnterpriseReport
   */
  void insert(GarbageEnterpriseReport garbageEnterpriseReport);

  /**
   * 根据id编辑
   * @param garbageEnterpriseReport
   */
  void updateOne(GarbageEnterpriseReport garbageEnterpriseReport);

  /**
   * @Description: 根据id查询企业申报详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbageEnterpriseReportVo findById(Long id);

  /**
   * @Description: 审核企业申报
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  void updateStatusById(GarbageEnterpriseReport garbageEnterpriseReport);

  /**
   * 保存附件（仅更新附件）
   * @param garbageEnterpriseReport
   */
  void saveFile(GarbageEnterpriseReport garbageEnterpriseReport);
}
