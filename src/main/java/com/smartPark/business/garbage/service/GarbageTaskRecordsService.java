package com.smartPark.business.garbage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.garbage.entity.GarbageTaskRecords;
import com.smartPark.business.garbage.entity.dto.GarbageMapDTO;
import com.smartPark.business.garbage.entity.vo.GarbageTaskRecordsDetailVo;
import com.smartPark.business.garbage.entity.vo.GarbageTaskRecordsVo;
import com.smartPark.common.base.model.RequestModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 垃圾收运计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface GarbageTaskRecordsService extends IService<GarbageTaskRecords> {

  IPage<GarbageTaskRecordsVo> queryListByPage(RequestModel<GarbageTaskRecordsVo> requestModel);

  /**
   * 增加
   * @param garbageTaskRecordsDetailVo
   */
  void insertDetail(GarbageTaskRecordsDetailVo garbageTaskRecordsDetailVo);

  /**
   * @Description: 根据id查询车辆详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbageTaskRecordsVo findById(Long id);

  /**
   * @Description: 根据收运记录详情id查询详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbageTaskRecordsDetailVo findDetailByDId(Long detailId);

  /**
   * @Description: 编辑收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  void updateDeTailOne(GarbageTaskRecordsDetailVo garbageTaskRecordsDetailVo);

  /**
   * @Description: 删除收运记录详情（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  void delDetailBatch(List<Long> ids);

  /**
   * @Description: 根据条件，分页(不分页)查询收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  IPage<GarbageTaskRecordsDetailVo> queryDetailListByPage(RequestModel<GarbageTaskRecordsVo> requestModel);

  /**
   * @Description: 编辑收运记录
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  void updateOne(GarbageTaskRecordsVo garbageTaskRecordsVo);

  /**
   * @Description: 总体概览
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbageMapDTO overview(GarbageMapDTO garbageMapDTO);

  /**
   * 导出
   * @param recordsVo
   * @param request
   * @param response
   * @return
   */
  Long export(GarbageTaskRecordsVo recordsVo, HttpServletRequest request, HttpServletResponse response);

}
