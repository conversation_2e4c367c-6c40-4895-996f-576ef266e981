package com.smartPark.business.garbage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.garbage.entity.GarbageTaskRecordsDetail;
import com.smartPark.business.garbage.entity.vo.GarbageTaskRecordsDetailVo;
import com.smartPark.business.garbage.entity.vo.GarbageTaskRecordsVo;
import com.smartPark.business.garbage.service.GarbageTaskRecordsService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 餐厨垃圾管理(垃圾运收管理)/收运记录管理
 * @author: kan yuanfeng
 * @Date: 2023/04/17 11:42
 * @Description: 收运记录管理
 */
@RestController
@RequestMapping("garbageTaskRecords")
@Api(tags = "餐厨垃圾管理(垃圾运收管理)/收运记录管理")
public class GarbageTaskRecordsController {
  
  @Autowired
  private GarbageTaskRecordsService garbageTaskRecordsService;

  /**
   * @Description: 删除收运记录详情（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @DeleteMapping
  @ApiOperation("删除收运记录详情（包含批量删除）")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "", desc = "删除收运记录")
  public RestMessage delDetailBatch(@RequestBody GarbageTaskRecordsDetail garbageTaskRecordsDetail){
    Assert.notEmpty(garbageTaskRecordsDetail.getIds(),"id不能为空");
    garbageTaskRecordsService.delDetailBatch(garbageTaskRecordsDetail.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 新增收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PostMapping
  @ApiOperation("新增收运记录详情")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "", desc = "新增收运记录")
  public RestMessage insertDetail(@RequestBody GarbageTaskRecordsDetailVo garbageTaskRecordsDetailVo){
    garbageTaskRecordsService.insertDetail(garbageTaskRecordsDetailVo);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PutMapping
  @ApiOperation("编辑收运记录详情")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "", desc = "编辑收运记录")
  public RestMessage updateDeTailOne(@RequestBody GarbageTaskRecordsDetailVo garbageTaskRecordsDetailVo){
    Assert.notNull(garbageTaskRecordsDetailVo.getId(),"id不能为空");
    garbageTaskRecordsService.updateDeTailOne(garbageTaskRecordsDetailVo);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑收运记录
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PutMapping("updateOne")
  @ApiOperation("编辑收运记录详情")
  public RestMessage updateOne(@RequestBody GarbageTaskRecordsVo garbageTaskRecordsVo){
    Assert.notNull(garbageTaskRecordsVo.getId(),"id不能为空");
    garbageTaskRecordsService.updateOne(garbageTaskRecordsVo);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询收运记录详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    GarbageTaskRecordsVo garbageTaskRecordsVo = garbageTaskRecordsService.findById(id);
    return RestBuilders.successBuilder().data(garbageTaskRecordsVo).build();
  }

  /**
   * @Description: 根据收运记录详情id查询详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @GetMapping("detail/{detailId}")
  @ApiOperation("根据收运记录详情id查询详情")
  public RestMessage findDetailByDId(@PathVariable("detailId")Long detailId) {
    Assert.notNull(detailId,"id不能为空");
    GarbageTaskRecordsDetailVo garbageTaskRecordsDetailVo = garbageTaskRecordsService.findDetailByDId(detailId);
    return RestBuilders.successBuilder().data(garbageTaskRecordsDetailVo).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<GarbageTaskRecordsVo> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<GarbageTaskRecordsVo> record = garbageTaskRecordsService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询收运记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PostMapping("detail/list")
  @ApiOperation("根据条件，分页(不分页)查询收运记录详情")
  public RestMessage queryDetailListByPage(@RequestBody RequestModel<GarbageTaskRecordsVo> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<GarbageTaskRecordsDetailVo> record = garbageTaskRecordsService.queryDetailListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * @Description: 根据条件导出
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("detail/export")
  @ApiOperation("导出")
  public RestMessage export(@RequestBody GarbageTaskRecordsVo recordsVo, HttpServletRequest request, HttpServletResponse response) {
    Long taskId = garbageTaskRecordsService.export(recordsVo, request, response);
    return RestBuilders.successBuilder().data(taskId).build();
  }
}

