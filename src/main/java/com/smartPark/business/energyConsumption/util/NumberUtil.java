package com.smartPark.business.energyConsumption.util;

import java.math.BigDecimal;

/**
 * <p>数字处理工具类</p>
 *
 * <AUTHOR>
 * @since 2021/3/9 17:43
 */
public class NumberUtil {

    /**
     * 小数 四舍五入，保留n位小数
     * @param doubleVal 原始值
     * @param newScale 保留小数位数
     * @return 四舍五入，保留n位小数 后的数
     */
    public static double doubleRoundHalfUp(Double doubleVal, int newScale) {
        return new BigDecimal(doubleVal).setScale(newScale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static BigDecimal bigDecimalRoundHalfUp(BigDecimal val, int newScale) {
        return val.setScale(newScale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 加法
     * @param v1 被加数
     * @param v2 加数
     * @param scale 保留几位小数
     */
    public static double add(Double v1, Double v2, int scale){
        BigDecimal b1 = new BigDecimal(Double.toString(v1 == null? 0.0 : v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2 == null? 0.0 : v2));
        return doubleRoundHalfUp(b1.add(b2).doubleValue(), scale);
    }

    /**
     * 减法
     * @param v1 被减数
     * @param v2 减数
     * @param scale 保留几位小数
     */
    public static double sub(Double v1, Double v2, int scale)  {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return doubleRoundHalfUp(b1.subtract(b2).doubleValue(), scale);
    }


    /**
     * 乘法
     * @param v1 被乘数
     * @param v2 乘数
     * @param scale 保留几位小数
     */
    public static double mul(Double v1, Double v2, int scale)  {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return doubleRoundHalfUp(b1.multiply(b2).doubleValue(), scale);
    }

    /**
     * 除法
     * @param v1 被除数
     * @param v2 除数
     * @param scale 保留几位小数
     */
    public static double div(Double v1, Double v2, int scale)  {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue(); //ROUND_HALF_UP:四舍五入
    }



}
