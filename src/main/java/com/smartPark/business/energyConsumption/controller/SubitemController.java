package com.smartPark.business.energyConsumption.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.energyConsumption.entity.Subitem;
import com.smartPark.business.energyConsumption.service.SubitemService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant.LogOperateActionType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.Serializable;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 能源消耗/分项管理
 *
 * <AUTHOR>
 * @since 2023-04-22
 */
@RestController
@RequestMapping("/energyConsumption/subitem")
@Api(tags = "分项管理")
public class SubitemController {
  /**
   * 服务对象
   */
  @Resource
  private SubitemService subitemService;

  /**
   * 分页查询所有数据
   *
   * @param requestModel 查询分页对象
   * @return 所有数据
   */
  @PostMapping("getPage")
  @ApiOperation("查询分页")
  public RestMessage selectPage(@RequestBody RequestModel<Subitem> requestModel) {
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<Subitem> record = subitemService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 查询所有数据
   *
   * @param requestModel 查询分页对象
   * @return 所有数据
   */
  @PostMapping("findAll")
  @ApiOperation("查询所有数据")
  public RestMessage findAll() {
    List<Subitem> record = subitemService.findAll();
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @GetMapping("{id}")
  @ApiOperation("查询单条")
  public RestMessage selectOne(@PathVariable Serializable id) {
    return RestBuilders.successBuilder((this.subitemService.getOneById(id))).build();
  }

  /**
   * 新增数据
   *
   * @param livableSubitem 实体对象
   * @return 新增结果
   */
  @PostMapping
  @ApiOperation("新增")
  @BusinessLogAnnotate(actionType = LogOperateActionType.ADD,menuCode = "consumption:baseInfo:subItem:add",desc = "增加分项")
  public RestMessage insert(@RequestBody Subitem livableSubitem) {
    return RestBuilders.successBuilder().success((this.subitemService.saveOne(livableSubitem))).build();
  }

  /**
   * 修改数据
   *
   * @param livableSubitem 实体对象
   * @return 修改结果
   */
  @PutMapping
  @ApiOperation("修改单条")
  @BusinessLogAnnotate(actionType = LogOperateActionType.EDIT,menuCode = "consumption:baseInfo:subItem:edit",desc = "编辑分项")
  public RestMessage update(@RequestBody Subitem livableSubitem) {
    return RestBuilders.successBuilder().success(this.subitemService.updateOne(livableSubitem)).build();
  }

  /**
   * 删除数据
   *
   * @param idList 主键结合
   * @return 删除结果
   */
  @DeleteMapping
  @ApiOperation("批量删除")
  @BusinessLogAnnotate(actionType = LogOperateActionType.DEL,menuCode = "consumption:baseInfo:subItem:del",desc = "批量删除")
  public RestMessage delete(@RequestParam("idList") List<Long> idList) {
    return RestBuilders.successBuilder().success(this.subitemService.deleteByIds(idList)).build();
  }
}
