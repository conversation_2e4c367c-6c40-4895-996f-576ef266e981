package com.smartPark.business.emergency.department.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_department_resources_ref")
public class DepartmentResourcesRef {

    /**
     * 主键,自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 部门id
     */
    @TableField("department_id_")
    private Long departmentId;

    /**
     * 资源关联类型 1-关联物资 2-关联部门
     */
    @TableField("type_")
    private Integer type;

    /**
     * 关联资源id
     */
    @TableField("ref_supply_id_")
    private Long refSupplyId;

    /**
     * 关联部门id
     */
    @TableField("ref_department_id_")
    private Long refDepartmentId;

    /**
     * 资源数量
     */
    @TableField("quantity_")
    private Integer quantity;
}
