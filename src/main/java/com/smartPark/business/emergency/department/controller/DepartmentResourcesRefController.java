package com.smartPark.business.emergency.department.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.emergency.department.entity.DepartmentResourcesRef;
import com.smartPark.business.emergency.department.entity.vo.DepartmentResourcesRefVo;
import com.smartPark.business.emergency.department.entity.vo.DepartmentWorkerRefVo;
import com.smartPark.business.emergency.department.service.DepartmentResourcesRefService;
import com.smartPark.business.emergency.department.service.DepartmentWorkerRefService;
import com.smartPark.common.alarm.entity.vo.AlarmVo;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;

/**
 * 应急资源管理/监测演练/部门管理
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@RestController
@RequestMapping("/safeDepartmentResourcesRef")
@Api(tags = "部门资源关联")
public class DepartmentResourcesRefController extends ApiController {

    @Autowired
    private DepartmentResourcesRefService departmentResourcesRefService;

    /**
     * 列表查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/list")
    @ApiOperation("查询列表")
    public RestMessage queryListByPage(@RequestBody RequestModel<DepartmentResourcesRefVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DepartmentResourcesRefVo> record = departmentResourcesRefService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

}
