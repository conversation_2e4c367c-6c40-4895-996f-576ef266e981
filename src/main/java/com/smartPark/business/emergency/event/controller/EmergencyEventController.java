package com.smartPark.business.emergency.event.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.emergency.event.entity.EmergencyEvent;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventDTO;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventTaskDTO;
import com.smartPark.business.emergency.event.entity.vo.EmergencyEventTaskVo;
import com.smartPark.business.emergency.event.entity.vo.EmergencyEventVo;
import com.smartPark.business.emergency.event.service.EmergencyEventService;
import com.smartPark.business.emergency.event.service.EmergencyEventTaskService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 应急资源管理/应急监测/应急事件
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@RestController
@RequestMapping("/safeEmergencyEvent")
public class EmergencyEventController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private EmergencyEventService emergencyEventService;

    @Resource
    private EmergencyEventTaskService emergencyEventTaskService;

    /**
     * 事件Dto列表查询
     *
     * @param requestModel 查询分页对象
     * @return 统一出参
     */
    @PostMapping("/selectEventDtoPage")
    public RestMessage selectEventDtoPage(@RequestBody RequestModel<EmergencyEventVo> requestModel,@RequestHeader(value = "Privilege-Code",required = false) String privilegeCode) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
//        String privilegeCode = request.getHeader("privilegeCode");
        IPage<EmergencyEventDTO> record = emergencyEventService.selectEventDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams(),privilegeCode);
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 事件Dto列表导出
     *
     * @param eventVo  事件vo
     * @param request  request
     * @param response response
     * @return 统一出参
     */
    @PostMapping("/selectEventDtoPageExport")
    public RestMessage selectEventDtoPageExport(@RequestBody EmergencyEventVo eventVo, HttpServletRequest request, HttpServletResponse response) {
        Long taskId = emergencyEventService.selectEventDtoPageExport(eventVo, request, response);
        return RestBuilders.successBuilder().data(taskId).build();
    }


    /**
     * 事件详情
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/eventDetail/{id}")
    public RestMessage eventDetail(@PathVariable Serializable id) {
        Assert.notNull(id, "id 不能为空");
        EmergencyEventVo eventVo = emergencyEventService.eventDetail(id);
        return RestBuilders.successBuilder(eventVo).build();
    }


    /**
     * 删除事件
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @DeleteMapping("/eventBatchDelete")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "emergencyResponse:emergencyMonitoring:event:del", desc = "删除应急事件")
    public RestMessage eventBatchDelete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(emergencyEventService.eventBatchDelete(idList)).build();
    }

    /**
     * 告警驱动事件预案启动
     *
     * @param alarmIdList 告警信息ids
     * @return 统一出参数
     */
    @PostMapping("/alarmTriggerEventPlan")
    public RestMessage alarmTriggerEventPlan(@RequestParam("idList") List<Long> alarmIdList) {
        return emergencyEventService.alarmTriggerEventPlan(alarmIdList);
    }

    /**
     * 分页查询事件任务dto(详情里用)
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectEventTaskDtoPage")
    public RestMessage selectEventTaskDtoPage(@RequestBody RequestModel<EmergencyEventTaskVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<EmergencyEventTaskDTO> record = emergencyEventTaskService.selectEventTaskDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }



    /**
     * 修改事件状态(暂停、恢复、终止)
     *
     * @param safeEmergencyEvent 实体对象
     * @return 修改结果
     */
    @PutMapping("/updateEventStatus")
    public RestMessage updateEventStatus(@RequestBody EmergencyEvent safeEmergencyEvent) {
        return RestBuilders.successBuilder().success(emergencyEventService.updateEventStatus(safeEmergencyEvent)).build();
    }



    /**
     * 关联事件类型(来源数据)
     * @return 统一出参
     */
    @PostMapping("/joinEventTypeFromData/{type}")
    public RestMessage joinEventTypeFromData(@PathVariable String type) {
        return emergencyEventTaskService.joinEventTypeFromData(type);
    }

    /**
     * 查询事件下的预案
     * @param eventId 事件id
     * @return 统一出参
     */
    @PostMapping("/selectEventPlan/{eventId}")
    public RestMessage selectEventPlan(@PathVariable Serializable eventId) {
        return emergencyEventTaskService.selectEventPlan(eventId);
    }

    /**
     * 查询事件任务流程图信息
     * @param eventPlanId 事件预案id
     * @return 统一出参
     */
    @PostMapping("/selectEventTaskProcess/{eventPlanId}")
    public RestMessage selectEventTaskProcess(@PathVariable Serializable eventPlanId) {
        return emergencyEventTaskService.selectEventTaskProcess(eventPlanId);
    }

    /**
     * 新增模拟演练事件
     * @param eventVo 事件vo
     * @return 统一出参
     */
    @PostMapping("/addSimulationExerciseEvent")
    public RestMessage addSimulationExerciseEvent(@RequestBody EmergencyEventVo eventVo) {
        return emergencyEventService.addSimulationExerciseEvent(eventVo);
    }


}

