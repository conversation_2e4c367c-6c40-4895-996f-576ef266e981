package com.smartPark.business.emergency.event.util;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 流程图对象,用于计算最长执行时间
 * <AUTHOR>
 */
@Data
public class ProcessGraph {
    /**
     * 顶点个数
     */
    private int numVertices;
    /**
     * 邻接表
     */
    private List<List<Integer>> adjList;
    /**
     * 顶点执行时间(边长)
     */
    private Double[] executionTimes;

    /**
     * 构造函数
     * @param numVertices 顶点个数
     * @param executionTimes 顶点执行时间(边长)
     */
    public ProcessGraph(int numVertices, Double[] executionTimes) {
        this.numVertices = numVertices;
        this.executionTimes = executionTimes;
        adjList = new ArrayList<>(numVertices);
        for (int i = 0; i < numVertices; i++) {
            adjList.add(new ArrayList<>());
        }
    }

    /**
     * 添加边
     * @param src 源顶点
     * @param dest 目标顶点
     */
    public void addEdge(int src, int dest) {
        adjList.get(src).add(dest);
    }

    /**
     * 计算最长执行时间
     * @return 最长执行时间
     */
    public Double findMaxExecutionTime() {
        Double[] maxExecutionTimes = new Double[numVertices];
        Arrays.fill(maxExecutionTimes, -1d);

        Double maxExecutionTime = 0d;

        for (int i = 0; i < numVertices; i++) {
            Double currentExecutionTime = dfs(i, maxExecutionTimes);
            if (currentExecutionTime > maxExecutionTime) {
                maxExecutionTime = currentExecutionTime;
            }
        }

        return maxExecutionTime;
    }

    /**
     * 深度优先搜索
     * @param vertex 顶点
     * @param maxExecutionTimes 最长执行时间数组
     * @return 最长执行时间
     */
    private Double dfs(int vertex, Double[] maxExecutionTimes) {
        if (maxExecutionTimes[vertex] != -1) {
            return maxExecutionTimes[vertex];
        }

        Double maxExecutionTime = executionTimes[vertex];

        for (int neighbor : adjList.get(vertex)) {
            Double neighborExecutionTime = dfs(neighbor, maxExecutionTimes);
            if (neighborExecutionTime + executionTimes[vertex] > maxExecutionTime) {
                maxExecutionTime = neighborExecutionTime + executionTimes[vertex];
            }
        }

        maxExecutionTimes[vertex] = maxExecutionTime;
        return maxExecutionTime;
    }

    public static void main(String[] args) {
        Double[] executionTimes = {0d,3d, 4d, 5d, 2d, 3d,0d};

        ProcessGraph graph = new ProcessGraph(7, executionTimes);
        graph.addEdge(0, 1);
        graph.addEdge(0, 2);
        graph.addEdge(1, 3);
        graph.addEdge(1, 4);
        graph.addEdge(2, 3);
        graph.addEdge(3, 5);
        graph.addEdge(3, 5);
        //graph.addEdge(3, 5);

        Double maxExecutionTime = graph.findMaxExecutionTime();
        System.out.println("Max execution time: " + maxExecutionTime);
    }
}
