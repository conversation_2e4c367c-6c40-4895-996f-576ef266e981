package com.smartPark.business.emergency.event.entity.dto;

import com.smartPark.business.emergency.event.entity.EmergencyEventExecutor;
import com.smartPark.business.emergency.person.entity.dto.EmergencyPersonDTO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * EmergencyEventExecutor实体类DTO
 *
 * <AUTHOR>
 * @date 2023/09/21
 */

@Data
@Accessors(chain = true)
public class EmergencyEventExecutorDTO extends EmergencyEventExecutor {
    /**
     * 应急人员dto
     */
    private EmergencyPersonDTO emergencyPersonDto;
}
