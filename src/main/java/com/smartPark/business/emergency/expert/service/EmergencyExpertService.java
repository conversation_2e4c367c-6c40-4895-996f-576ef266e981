package com.smartPark.business.emergency.expert.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.expert.entity.EmergencyExpert;
import com.smartPark.business.emergency.expert.entity.dto.EmergencyExpertDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencyExpert表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/21
 */
public interface EmergencyExpertService extends IService<EmergencyExpert> {

    /**
     * 新增
     *
     * @param safeEmergencyExpertDTO 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencyExpertDTO safeEmergencyExpertDTO);

    /**
     * 修改单条
     *
     * @param safeEmergencyExpertDTO 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencyExpertDTO safeEmergencyExpertDTO);

    /**
     * 查询分页
     *
     * @param page                分页对象
     * @param safeEmergencyExpert 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyExpert> selectPage(Page page, EmergencyExpert safeEmergencyExpert);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyExpert getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Integer> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencyExpert 过滤条件实体对象
     * @param request             请求
     * @param response            响应
     */
    Long export(EmergencyExpert safeEmergencyExpert, HttpServletRequest request, HttpServletResponse response);

    /**
     * @Description: 导入
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    Long imports(MultipartFile file) throws IOException;
}

