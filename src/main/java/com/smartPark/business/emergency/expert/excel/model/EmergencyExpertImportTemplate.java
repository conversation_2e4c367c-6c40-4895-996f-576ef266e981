package com.smartPark.business.emergency.expert.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @Description 导入模板
 * @<PERSON> <PERSON><PERSON>
 * @Date 2023/3/28 13:42
 */
@Data
public class EmergencyExpertImportTemplate {
    @ExcelProperty("专家姓名")
    @ColumnWidth(value = 30)
    private String name;

    @ExcelProperty("性别")
    @ColumnWidth(value = 30)
    private String genderStr;

    @ExcelProperty("专家类别")
    @ColumnWidth(value = 30)
    private String categoryStr;

    @ExcelProperty("联系电话")
    @ColumnWidth(value = 30)
    private String phone;

    @ExcelProperty("职称")
    @ColumnWidth(value = 30)
    private String jobTitle;

    @ExcelProperty("健康状况")
    @ColumnWidth(value = 30)
    private String healthStr;

    @ExcelProperty("出生日期")
    @ColumnWidth(value = 30)
    private String birthdayStr;

    @ExcelProperty("身份证号码")
    @ColumnWidth(value = 30)
    private String idCard;

    @ExcelProperty("家庭住址")
    @ColumnWidth(value = 30)
    private String address;

    @ExcelProperty("工作单位")
    @ColumnWidth(value = 30)
    private String company;

    @ExcelProperty("参加工作时间")
    @ColumnWidth(value = 30)
    private String workTimeStr;

    @ExcelProperty("工作单位主管部门")
    @ColumnWidth(value = 30)
    private String superiorDepartment;

    @ExcelProperty("最高学历")
    @ColumnWidth(value = 30)
    private String highestDegree;

    @ExcelProperty("毕业院校")
    @ColumnWidth(value = 30)
    private String graduateSchool;

    @ExcelProperty("专业类别")
    @ColumnWidth(value = 30)
    private String majorCategory;

    @ExcelProperty("所属专家组")
    @ColumnWidth(value = 30)
    private String expertGroup;

    @ExcelProperty("专长描述")
    @ColumnWidth(value = 30)
    private String specialty;

    @ExcelProperty("工作简历")
    @ColumnWidth(value = 30)
    private String workExperience;

    @ExcelProperty("备注")
    @ColumnWidth(value = 30)
    private String remark;
}
