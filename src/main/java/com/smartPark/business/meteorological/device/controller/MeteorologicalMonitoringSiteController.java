package com.smartPark.business.meteorological.device.controller;


import com.smartPark.business.meteorological.device.entity.MeteorologicalMonitoringSite;
import com.smartPark.business.meteorological.device.service.MeteorologicalMonitoringSiteService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * 气象环境/监测站点
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@RestController
@RequestMapping("/meteorological/meteorologicalMonitoringSite")
public class MeteorologicalMonitoringSiteController {

  @Resource
  private MeteorologicalMonitoringSiteService meteorologicalMonitoringSiteService;

  /**
   * 增加监测站点
   * @param meteorologicalMonitoringSite
   * @return
   */
  @PostMapping
  @ApiOperation("增加监测站点")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "environmentMonitoring:meteorologicalEnvironment:deviceManage:add", desc = "增加气象环境设备")
  public RestMessage insert(@RequestBody MeteorologicalMonitoringSite meteorologicalMonitoringSite){
    //参数验证
    meteorologicalMonitoringSiteService.insert(meteorologicalMonitoringSite);
    return RestBuilders.successBuilder().build();
  }

  /**
   * 删除增加监测站点（包含批量删除）
   * @param meteorologicalMonitoringSite
   * @return
   */
  @DeleteMapping
  @ApiOperation("删除监测站点（包含批量删除）")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "environmentMonitoring:meteorologicalEnvironment:deviceManage:batchDelete", desc = "删除监测站点")
  public RestMessage delBatch(@RequestBody MeteorologicalMonitoringSite meteorologicalMonitoringSite){
    Assert.notEmpty(meteorologicalMonitoringSite.getIds(),"id不能为空");
    meteorologicalMonitoringSiteService.delBatch(meteorologicalMonitoringSite.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * 编辑监测站点
   * @param meteorologicalMonitoringSite
   * @return
   */
  @PutMapping
  @ApiOperation("编辑监测站点")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "environmentMonitoring:meteorologicalEnvironment:deviceManage:edit", desc = "编辑监测站点")
  public RestMessage updateById(@RequestBody MeteorologicalMonitoringSite meteorologicalMonitoringSite){
    Assert.notNull(meteorologicalMonitoringSite.getId(),"id不能为空");
    meteorologicalMonitoringSiteService.updateOne(meteorologicalMonitoringSite);
    return RestBuilders.successBuilder().build();
  }

  /**
   * 根据id查询监测站点详情
   * @param id
   * @return
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询监测站点详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    MeteorologicalMonitoringSite meteorologicalMonitoringSite = meteorologicalMonitoringSiteService.findById(id);
    return RestBuilders.successBuilder().data(meteorologicalMonitoringSite).build();
  }

  /**
   * 查询所有监测站点列表
   * @return
   */
  @PostMapping("list")
  @ApiOperation("查询所有监测站点列表")
  public RestMessage queryList(){
    List<MeteorologicalMonitoringSite> record = meteorologicalMonitoringSiteService.queryList();
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * 查询所有有设备的监测站点列表
   * @return
   */
  @PostMapping("listHasDevice")
  @ApiOperation("查询所有有设备的监测站点列表")
  public RestMessage queryListHasDevice(){
    List<MeteorologicalMonitoringSite> record = meteorologicalMonitoringSiteService.queryListHasDevice();
    return RestBuilders.successBuilder().data(record).build();
  }

}
