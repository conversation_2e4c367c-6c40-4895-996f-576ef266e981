package com.smartPark.business.gardensoil.device.entity.vo;

import com.smartPark.business.gardensoil.device.entity.GardenSoilDevice;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 园林土壤设备
 * @Description 园林土壤设备
 * <AUTHOR>
 * @Date 2023/3/21 11:44
 */
@Data
public class GardenSoilDeviceVo extends GardenSoilDevice {
    /**
     * 设备名称
     */
    private String sbmc;

    /**
     *设备标识码
     */
    private String bsm;

    private String szjd;
    private String szsq;
    private String szdywg;

    /**
     * 区域全路径
     */
    private String areaPath;

    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;

    /**
     * 在线状态（1在线0离线）
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date lastPushTime;

    /**
     * 告警状态
     * @apiNote 0:正常; 1:告警
     */
    private Integer alarmState;

    /**
     * 权属单位
     */
    private String ownerEnterpriseName;

    /**
     * 位置定位信息X坐标
     */
    private Double objX;

    /**
     * 位置定位信息Y坐标
     */
    private Double objY;
}
