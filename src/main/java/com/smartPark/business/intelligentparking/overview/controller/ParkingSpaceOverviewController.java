package com.smartPark.business.intelligentparking.overview.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseDistribution;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseInfo;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUsePeriod;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseRank;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUseDistributionService;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUseInfoService;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUsePeriodService;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUseRankService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * 智慧停车/停车概览
 *
 * <AUTHOR>
 * @date 2023/05/11
 */
@Slf4j
@RestController
@RequestMapping("trafficParkingSpaceOverview")
public class ParkingSpaceOverviewController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private ParkingSpaceUseInfoService trafficParkingSpaceUseInfoService;

    @Resource
    private ParkingSpaceUsePeriodService trafficParkingSpaceUsePeriodService;


    @Resource
    private ParkingSpaceUseRankService trafficParkingSpaceUseRankService;

    @Resource
    private ParkingSpaceUseDistributionService trafficParkingSpaceUseDistributionService;


    @PostMapping("selectParkingSpaceUseInfo")
    @ApiOperation("查询车位空余信息")
    public RestMessage selectParkingSpaceUseInfo(@RequestBody ParkingSpaceUseInfo parkingSpaceUseInfo) {

        ParkingSpaceUseInfo record = trafficParkingSpaceUseInfoService.selectParkingSpaceUseInfo(parkingSpaceUseInfo);
        return RestBuilders.successBuilder(record).build();
    }


    @PostMapping("selectParkingSpaceUsePeriod")
    @ApiOperation("查询车位使用趋势")
    public RestMessage selectParkingSpaceUsePeriod(@RequestBody ParkingSpaceUsePeriod parkingSpaceUsePeriod) {

        List<ParkingSpaceUsePeriod> record = trafficParkingSpaceUsePeriodService.selectParkingSpaceUsePeriod(parkingSpaceUsePeriod);
        return RestBuilders.successBuilder(record).build();
    }


    @PostMapping("selectParkingSpaceUseRank")
    @ApiOperation("使用排行")
    public RestMessage selectParkingSpaceUseRank(@RequestBody ParkingSpaceUseRank parkingSpaceUseRank) {

        List<ParkingSpaceUseRank> record = trafficParkingSpaceUseRankService.selectParkingSpaceUseRank(parkingSpaceUseRank);
        return RestBuilders.successBuilder(record).build();
    }

    @PostMapping("selectParkingSpaceUseDistribution")
    @ApiOperation("时长分布图")
    public RestMessage selectParkingSpaceUseDistribution(@RequestBody ParkingSpaceUseDistribution parkingSpaceUseDistribution) {
        List<ParkingSpaceUseDistribution> record = trafficParkingSpaceUseDistributionService.selectParkingSpaceUseDistribution(parkingSpaceUseDistribution);
        return RestBuilders.successBuilder(record).build();
    }

    @PostMapping("selectSumUseDistribution")
    @ApiOperation("查询某天全部停车场的停车时长分布情况")
    public RestMessage selectSumUseDistribution(@RequestBody ParkingSpaceUseDistribution parkingSpaceUseDistribution) {
        Assert.notNull(parkingSpaceUseDistribution,"参数为空");
        Assert.notNull(parkingSpaceUseDistribution.getStatisticTime(),"统计时间不能为空");
        List<ParkingSpaceUseDistribution> record = trafficParkingSpaceUseDistributionService.selectSumUseDistribution(parkingSpaceUseDistribution);
        return RestBuilders.successBuilder(record).build();
    }

    @PostMapping("/selectDayUseInfo")
    public RestMessage selectDayUseInfo(@RequestBody ParkingSpaceUsePeriod parkingSpaceUsePeriod){
        Assert.notNull(parkingSpaceUsePeriod,"参数为空");
        Assert.notNull(parkingSpaceUsePeriod.getStatisticTime(),"统计时间不能为空");
        List<ParkingSpaceUsePeriod> record = trafficParkingSpaceUsePeriodService.selectDayUseInfo(parkingSpaceUsePeriod);
        return RestBuilders.successBuilder(record).build();
    }
}

