package com.smartPark.business.intelligentparking.lot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLot;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLotFloor;
import com.smartPark.business.intelligentparking.lot.entity.dto.ParkingLotTreeDTO;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ParkingLot表服务接口
 *
 * <AUTHOR>
 * @since 2023/05/06
 */
public interface ParkingLotService extends IService<ParkingLot> {

    /**
     * 新增
     *
     * @param trafficParkingLot 实体对象
     * @return 操作结果
     */
    boolean saveOne(ParkingLot trafficParkingLot);

    /**
     * 修改单条
     *
     * @param trafficParkingLot 实体对象
     * @return 修改结果
     */
    boolean updateOne(ParkingLot trafficParkingLot);

    /**
     * 查询分页
     *
     * @param page              分页对象
     * @param trafficParkingLot 分页参数对象
     * @return 查询分页结果
     */
    IPage<ParkingLot> selectPage(Page page, ParkingLot trafficParkingLot);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    ParkingLot getOneById(Long id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param trafficParkingLot 过滤条件实体对象
     * @param request           请求
     * @param response          响应
     */
    void export(ParkingLot trafficParkingLot, HttpServletRequest request, HttpServletResponse response);

    boolean relateParkingSpace(ParkingLotFloor parkingLotFloor);

    /**
     * 停车场树
     * @return
     */
    List<ParkingLotTreeDTO> getTree();

  ParkingLot findByParkId(String parkId);
}

