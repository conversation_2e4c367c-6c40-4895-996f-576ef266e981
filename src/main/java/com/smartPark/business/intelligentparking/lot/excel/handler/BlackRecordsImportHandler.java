package com.smartPark.business.intelligentparking.lot.excel.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ErrorMsg;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.importer.DataImportParam;
import com.asyncexcel.core.importer.ImportContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.intelligentparking.lot.entity.InternalCar;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLot;
import com.smartPark.business.intelligentparking.lot.excel.model.BlackRecordsImportModel;
import com.smartPark.business.intelligentparking.lot.excel.model.CarWithImage;
import com.smartPark.business.intelligentparking.lot.mapper.ParkingLotMapper;
import com.smartPark.business.intelligentparking.lot.service.InternalCarService;
import com.smartPark.business.intelligentparking.lot.service.ParkingLotService;
import com.smartPark.common.asyncexcel.handler.CommonImportHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/22
 * @description
 */
@Slf4j
@ExcelHandle
public class BlackRecordsImportHandler extends CommonImportHandler<BlackRecordsImportModel> {

    @Resource
    private InternalCarService internalCarService;
    @Resource
    private ParkingLotService parkingLotService;
    @Resource
    private ParkingLotMapper parkingLotMapper;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ReadSheet readSheet = EasyExcel.readSheet().sheetNo(0).headRowNumber(1).build();
        ImportContext impCtx = (ImportContext) ctx;
        impCtx.setReadSheet(readSheet);
    }

    @Override
    public List<ErrorMsg> importData(List<BlackRecordsImportModel> list, DataImportParam param)
            throws Exception {
        Map<String, Object> paramMap = param.getParameters();
        List<CarWithImage> importsPreList = null;
        if (paramMap != null) {
            importsPreList = (List<CarWithImage>) paramMap.get("importsPreList");
        }
        List<ErrorMsg> errorList = new ArrayList<>();
        for (BlackRecordsImportModel recordsImportModel : list) {
            InternalCar internalCar = new InternalCar();
            ErrorMsg errorMsg = checkAndInitBean(param, recordsImportModel, internalCar);
            if (null != errorMsg) {
                errorList.add(errorMsg);
            } else {
                try {
                    internalCarService.saveOne(internalCar);
                } catch (Exception e) {
                    errorList.add(new ErrorMsg(recordsImportModel.getRow(), e.getMessage()));
                    log.error("导入报错", e);
                }
            }
        }

        deleteFileTemp(importsPreList);
        return errorList;
    }

    private void deleteFileTemp(List<CarWithImage> importsPreList) {
        // 删除本地文件
        for (CarWithImage carWithImage : importsPreList) {
            if (StringUtils.isNotBlank(carWithImage.getCard1())) {
                try {
                    Files.delete(new File(carWithImage.getCard1()).toPath());
                } catch (IOException e) {
                    log.error("删除本地文件失败", e);
                }
            }
            if (StringUtils.isNotBlank(carWithImage.getCard2())) {
                try {
                    Files.delete(new File(carWithImage.getCard2()).toPath());
                } catch (IOException e) {
                    log.error("删除本地文件失败", e);
                }
            }


        }

    }

    private InternalCar getBean(DataImportParam param, BlackRecordsImportModel recordsImportModel, InternalCar internalCar) {
        BeanUtils.copyProperties(recordsImportModel, internalCar);
        internalCar.setApplicantName(recordsImportModel.getApplicantName());
        internalCar.setPhone(recordsImportModel.getPhone());
        internalCar.setCarNo(recordsImportModel.getCarNo());
        // 1-白色、2-黑色、3-银色、4-红色、5-蓝色、6-金色、7-灰色、8-绿色、9-棕色、10-粉色、11-其它
        if (StringUtils.isNotBlank(recordsImportModel.getCarColor())) {
            internalCar.setCarColor("白色".equals(recordsImportModel.getCarColor()) ? 1 :
                    "黑色".equals(recordsImportModel.getCarColor()) ? 2 :
                            "银色".equals(recordsImportModel.getCarColor()) ? 3 :
                                    "红色".equals(recordsImportModel.getCarColor()) ? 4 :
                                            "蓝色".equals(recordsImportModel.getCarColor()) ? 5 :
                                                    "金色".equals(recordsImportModel.getCarColor()) ? 6 :
                                                            "灰色".equals(recordsImportModel.getCarColor()) ? 7 :
                                                                    "绿色".equals(recordsImportModel.getCarColor()) ? 8 :
                                                                            "棕色".equals(recordsImportModel.getCarColor()) ? 9 :
                                                                                    "粉色".equals(recordsImportModel.getCarColor()) ? 10 :
                                                                                            "其它".equals(recordsImportModel.getCarColor()) ? 11 : 0);
        }
        internalCar.setCarType(3);
        String lots = recordsImportModel.getRelateParkType();
        if (StringUtils.isNotBlank(lots)) {
            if ("全部".equals(lots)) {
                internalCar.setRelateParkType(1);
            } else if (lots != null) {
                String[] lotNames = lots.split(",");
                if (lotNames.length > 0) {
                    Set<Long> ids = parkingLotMapper.getIds(Arrays.asList(lotNames));
                    internalCar.setParkingLotIds(ids);
                    internalCar.setRelateParkType(2);
                }
            }
        }
        // 日期 时分秒置为0
        if (recordsImportModel.getInvalidDate() != null) {
            Calendar calendar1 = Calendar.getInstance();
            calendar1.setTime(recordsImportModel.getInvalidDate());
            calendar1.set(Calendar.HOUR_OF_DAY, 0);
            calendar1.set(Calendar.MINUTE, 0);
            calendar1.set(Calendar.SECOND, 0);
            calendar1.set(Calendar.MILLISECOND, 0);
            internalCar.setInvalidDate(calendar1.getTime());
        }
        if (recordsImportModel.getEffectiveDate() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(recordsImportModel.getEffectiveDate());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            internalCar.setEffectiveDate(calendar.getTime());
        }
        internalCar.setCreateTime(new Date());
        internalCar.setCreatorId(Long.valueOf(param.getCreateUserCode()));
        internalCar.setModifyTime(new Date());
        internalCar.setModifyId(Long.valueOf(param.getCreateUserCode()));
        return internalCar;
    }

    private ErrorMsg checkAndInitBean(DataImportParam param, BlackRecordsImportModel recordsImportModel, InternalCar internalCar) {
        try {
            internalCarService.validForExcel(getBean(param, recordsImportModel, internalCar));
        } catch (Exception e) {
            return new ErrorMsg(recordsImportModel.getRow(), e.getMessage());
        }
        return null;
    }
}
