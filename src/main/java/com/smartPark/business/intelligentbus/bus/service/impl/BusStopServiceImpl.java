package com.smartPark.business.intelligentbus.bus.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.intelligentbus.bus.entity.*;
import com.smartPark.business.intelligentbus.bus.entity.dto.BusStopDTO;
import com.smartPark.business.intelligentbus.bus.mapper.BusRouteMapper;
import com.smartPark.business.intelligentbus.bus.mapper.BusRouteRefStopDrivingInfoMapper;
import com.smartPark.business.intelligentbus.bus.mapper.BusStopMapper;
import com.smartPark.business.intelligentbus.bus.service.BusRouteRefBusStopService;
import com.smartPark.business.intelligentbus.bus.service.BusRouteService;
import com.smartPark.business.intelligentbus.bus.service.BusStopService;
import com.smartPark.business.intelligentbus.bus.service.BusStopStatisticService;
import com.smartPark.business.intelligentbus.openapi.entity.BusRouteApiDto;
import com.smartPark.business.intelligentbus.openapi.entity.BusStopApiDto;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BusStop表服务实现类
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@Slf4j
@Service("trafficBusStopService")
public class BusStopServiceImpl extends ServiceImpl
        <BusStopMapper, BusStop> implements BusStopService {
    @Resource
    private CommonService commonService;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BusRouteRefStopDrivingInfoMapper busRouteRefStopDrivingInfoMapper;
    @Resource
    private BusRouteMapper busRouteMapper;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }


    @Resource
    private BusRouteRefBusStopService busRouteRefBusStopService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("，");
        //删除与公交线路的关联
        new LambdaUpdateChainWrapper<>(busRouteRefBusStopService.getBaseMapper()).in(BusRouteRefBusStop::getBusStopId, idList).setSql("deleted_ = id_").update();
        for (Long id : idList) {
            BusStop busStop = baseMapper.selectById(id);
            if (!ObjectUtils.isEmpty(busStop)) {
                sj.add(busStop.getObjId());
                DeviceApplicationModelRef modelRef = getDeviceApplicationModelRef();
                modelRef.setActionType(EventUtil.DELETE);
                modelRef.setObjId(busStop.getObjId());
                //删除关系
                EventUtil.publishRefEvent(modelRef);
                removeById(id);
            }
        }
        LogHelper.setLogInfo("", idList.toString(), null, null, "删除公交站，部件码："+sj);
        return true;
    }


    @Override
    public boolean saveOne(BusStop trafficBusStop) {
        commonService.setCreateAndModifyInfo(trafficBusStop);
        validParamRequired(trafficBusStop);
        validRepeat(trafficBusStop);
        validParamFormat(trafficBusStop);
        //关联表
        //获取应用名，应用id
        DeviceApplicationModelRef device = getDeviceApplicationModelRef();
        device.setObjId(trafficBusStop.getObjId());
        //保存 关联库
        EventUtil.publishRefEvent(device);
        String msg = "部件码：" + trafficBusStop.getObjId();
        LogHelper.setLogInfo("", msg, null, msg, msg);
        return save(trafficBusStop);
    }

    private DeviceApplicationModelRef getDeviceApplicationModelRef() {
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.TRAFFIC);
        DeviceApplicationModelRef device = DeviceApplicationModelRef.getInstall(baseApplication);
        device.setModelId(DeviceModelConstant.BUS_STOP);
        return device;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(BusStop trafficBusStop) {
        Assert.notNull(trafficBusStop.getId(), "id不能为空");
        commonService.setModifyInfo(trafficBusStop);

        validRepeat(trafficBusStop);
        validParamFormat(trafficBusStop);
        return updateById(trafficBusStop);
    }

    @Override
    public IPage<BusStop> selectPage(Page page, BusStop trafficBusStop) {
        IPage<BusStop> r = baseMapper.selectPage(page, trafficBusStop);
        List<BusStop> busStopList = r.getRecords();
        if (busStopList.isEmpty()) {
            return r;
        }
//        for (BusStop record : r.getRecords()) {
//            List<BusRoute> routes = new ArrayList<>();
//            for (int i = 0; i < 2; i++) {
//                routes.add(new BusRoute().setRouteName("30" + i).setRouteDesc("光谷七路->软件园"));
//            }
//            record.setBusRouteList(routes);
//        }
        // 停车场idList
        List<Long> busStopIdList = busStopList.stream().map(BusStop::getId).collect(Collectors.toList());
        // 根据busStopId查所有关联的路线
        List<BusRoute> busRouteList = busRouteRefBusStopService.getRelationBusRoute(new BusRoute().setBusStopIds(busStopIdList), -1);
        // 存一份去重的BusRouteMap
        Map<Long, BusRoute> distinctBusRouteMap = new HashMap<>();
        busRouteList.stream().filter(e -> e.getId() != null).forEach((item)->{
            distinctBusRouteMap.putIfAbsent(item.getId(),item);
        });
        // 路线idList
        List<Long> busRouteIdList = new ArrayList<>(distinctBusRouteMap.keySet());
        // 路线正在运营车辆数量
        List<BusRoute> busRouteCarCountList = busRouteMapper.getRelationBusRouteCarCount(busRouteIdList);
        Map<Long, List<BusRoute>> busRouteCarCountMap = busRouteCarCountList.stream().collect(Collectors.groupingBy(BusRoute::getId));
        // 公交站 路线的最近一辆车时间
        // 先根据busStopId及BusRouteId查所有
        BusRouteRefStopDrivingInfo busRouteRefStopDrivingInfo = new BusRouteRefStopDrivingInfo();
        busRouteRefStopDrivingInfo.setBusStopIds(busStopIdList);
        busRouteRefStopDrivingInfo.setBusRouteIds(busRouteIdList);
        List<BusRouteRefStopDrivingInfo> busRouteRefStopDrivingInfoIPageList = busRouteRefStopDrivingInfoMapper.selectPage(new Page(1, -1), busRouteRefStopDrivingInfo).getRecords();
        // 根据busStopId及BusRouteId分组
        Map<String, List<BusRouteRefStopDrivingInfo>> collect = busRouteRefStopDrivingInfoIPageList.stream().collect(Collectors.groupingBy((item) -> item.getBusStopId() + "@" + item.getBusRouteId()));

        Map<Long, List<BusRoute>> map = busRouteList.stream().filter(e -> e.getBusStopId() != null).collect(Collectors.groupingBy(BusRoute::getBusStopId));

        for (BusStop item1 : busStopList) {
            List<BusRoute> temp = map.get(item1.getId());
            if (temp != null) {
                temp.forEach((item2)->{
                    List<BusRoute> busRoutes = busRouteCarCountMap.get(item2.getId());
                    if (!CollectionUtils.isEmpty(busRoutes)){
                        // 运营车辆数
                        item2.setBusNum(busRoutes.get(0).getBusNum());
                    }
                    List<BusRouteRefStopDrivingInfo> busRouteRefStopDrivingInfos = collect.get(item1.getId() + "@" + item2.getId());
                    if (!CollectionUtils.isEmpty(busRouteRefStopDrivingInfos)) {
                        // 路线到站最近时间
                        item2.setNearestInTime(busRouteRefStopDrivingInfos.get(0).getNearestInTime());
                    }
                });
            }
            if (temp != null && temp.size() > 3) {
                item1.setBusRouteList(temp.subList(0, 3));
            } else {
                item1.setBusRouteList(temp);
            }
        }
        return r;
    }

    @Override
    public IPage<BusStopApiDto> selectStopPage(Page page, BusStopApiDto trafficBusStop) {
        IPage<BusStopApiDto> r = baseMapper.selectStopPage(page, trafficBusStop);
        List<BusStopApiDto> busStopList = r.getRecords();
        if (busStopList.isEmpty()) {
            return r;
        }
        //将id与对象进行映射，方便后面进行获取
        Map<Long, BusStopApiDto> busStopMap = new HashMap<>();
        if (!Long.valueOf(r.getRecords().size()).equals(r.getTotal())){
            //如果不等于，则全量查
            for (BusStop busStop : baseMapper.selectPage(new Page(1, -1), new BusStop()).getRecords()) {
                BusStopApiDto busStopApiDto = new BusStopApiDto();
                busStopApiDto.setId(busStop.getId());
                busStopApiDto.setName(busStop.getObjName());
                busStopMap.put(busStop.getId(),busStopApiDto);
            }
        }else {
            busStopList.forEach((item)->{
                busStopMap.put(item.getId(),item);
            });
        }
        // 停车场idList
        List<Long> busStopIdList = busStopList.stream().map(BusStopApiDto::getId).collect(Collectors.toList());
        // 根据busStopId查所有关联的路线
        List<BusRoute> busRouteList = busRouteRefBusStopService.getRelationBusRoute(new BusRoute().setBusStopIds(busStopIdList), -1);
        Map<Long, List<BusRoute>> map = busRouteList.stream().filter(e -> e.getBusStopId() != null).collect(Collectors.groupingBy(BusRoute::getBusStopId));
        //因为要计算始发站，这里要获取到跟线路相关的所有公交站
        List<Long> busRouteIds = busRouteList.stream().map(BusRoute::getId).collect(Collectors.toList());
        List<BusRouteRefBusStop> records = busRouteRefBusStopService.selectPage(new Page(1, -1), new BusRouteRefBusStop().setBusRouteIds(busRouteIds)).getRecords();
        Map<Long, List<BusRouteRefBusStop>> busRouteRefBusStopMap = records.stream().collect(Collectors.groupingBy(BusRouteRefBusStop::getBusRouteId));
        //进行排序
        for (Long routeId : busRouteRefBusStopMap.keySet()){
            List<BusRouteRefBusStop> busRouteRefBusStops = busRouteRefBusStopMap.get(routeId);
            busRouteRefBusStops.sort(Comparator.comparing(BusRouteRefBusStop::getSortNo));
        }

        for (BusStopApiDto item : busStopList) {
            List<BusRoute> busRoutes = map.get(item.getId());
            if (CollectionUtil.isNotEmpty(busRoutes)) {
                List<BusRouteApiDto> busRouteApiDtos = new ArrayList<>();
                busRoutes.forEach((busRoute)->{
                    BusRouteApiDto busRouteApiDto = new BusRouteApiDto();
                    busRouteApiDto.setId(busRoute.getId());
                    busRouteApiDto.setName(busRoute.getRouteName());
                    List<BusRouteRefBusStop> busRouteRefBusStops = busRouteRefBusStopMap.get(busRoute.getId());
                    if (CollectionUtil.isNotEmpty(busRouteRefBusStops)){
                        //始发站
                        busRouteApiDto.setStart_stop(busStopMap.get(busRouteRefBusStops.get(0).getBusStopId()).getName());
                        //终点站
                        busRouteApiDto.setEnd_stop(busStopMap.get(busRouteRefBusStops.get(busRouteRefBusStops.size() - 1).getBusStopId()).getName());
                    }
                    busRouteApiDtos.add(busRouteApiDto);
                });
                item.setBus_lines(busRouteApiDtos);
            }
        }
        return r;
    }

    @Override
    public void export(BusStop trafficBusStop, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Resource
    ObjInfoMapper objInfoMapper;

    @Override
    public List<ObjInfo> getValidObjInfo(ObjInfo objInfo) {
        BusStop road = new BusStop().setObjInfo(objInfo).setObjId(objInfo.getObjId());
        validParamFormat(road);
        validRepeat(road);
        return objInfoMapper.selectList(new QueryWrapper<>(objInfo));
    }


    @Resource
    private BusRouteService busRouteService;


    @Override
    public BusStop getOneById(Serializable id) {
        BusStop r = baseMapper.getOneById(id);
        if (ObjectUtils.isEmpty(r)) {
            throw new BusinessException("数据不存在");
        }
//        r.setRouteDesc("301 光谷七路->软件园 15站;308 光谷七路->软件园 18站");
//        List<BusRoute> routes = new ArrayList<>();
//        for (int i = 0; i < 2; i++) {
//            routes.add(new BusRoute().setRouteName("30" + i).setRouteDesc("光谷七路->软件园").setBusStopNum(15 + i));
//        }
        List<BusRoute> routes = busRouteRefBusStopService.getRelationBusRoute(new BusRoute().setBusStopId(r.getId()), -1);
        // 计算车辆数
//        for (BusRoute route : routes) {
//            route.setBusNum(busRouteRefCarService.count(new QueryWrapper<>(new BusRouteRefCar().setBusRouteId(route.getId()))));
//        }
//        BusRouteServiceImpl.selectRouteWithBusNum(routes, busRouteRefCarService);
        if (routes != null && routes.size() > 0) {
            List<Long> ids = routes.stream().map(BusRoute::getId).collect(Collectors.toList());
            List<BusRoute>  busRouteList = busRouteService.selectRouteWithBusStopNum(new BusRoute().setIds(ids));
            if (busRouteList != null && busRouteList.size() > 0) {
                Map<Long, List<BusRoute>> map = busRouteList.stream().filter(e -> e.getId() != null).collect(Collectors.groupingBy(BusRoute::getId));
                for (BusRoute record : routes) {
                    List<BusRoute> ll = map.get(record.getId());
                    if (ll != null && ll.size() > 0) {
                        record.setBusStopNum(ll.get(0).getBusStopNum());
                    }
                }
            }
        }
        r.setBusRouteList(routes);
        return r;
    }

    /**
     * 校验重复
     */
    private void validRepeat(BusStop trafficBusStop) {

        List<BusStop> list = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BusStop::getObjId, trafficBusStop.getObjId())
                .list();
        if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(trafficBusStop.getId()) || !trafficBusStop.getId().equals(list.get(0).getId()))) {
            throw new BusinessException("公交编号已存在");
        }


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(BusStop trafficBusStop) {
        //Assert.notNull(trafficBusStop, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(trafficBusStop.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(BusStop trafficBusStop) {
        //Assert.isTrue(trafficBusStop.getName() == null || trafficBusStop.getName().length() <= 50,
        //        "名称超长");
    }

    @Resource
    private BusStopStatisticService busStopStatisticService;

    @Override
    public BusStopDTO getInfoForMap(BusStop busStop) {
        BusStop one1 = getOneById(busStop.getId());
        if (ObjectUtils.isEmpty(one1)) {
            return null;
        }
        BusStopDTO one = new BusStopDTO();
        BeanUtils.copyProperties(one1, one);
        new LambdaQueryChainWrapper<>(busStopStatisticService.getBaseMapper()).eq(BusStopStatistic::getBusStopId, busStop.getId()).list().forEach(busStopStatistic -> {
            one.setGrabImg(busStopStatistic.getGrabImg());
            one.setPersonNum(busStopStatistic.getPersonNum());
            one.setNearestInBusNum(busStopStatistic.getNearestInBusNum());
            one.setJustOutTimeBusNum(busStopStatistic.getJustOutTimeBusNum());
            one.setDroveNum(busStopStatistic.getDroveNum());
        });
        List<BusRoute> busRouteList = busRouteRefBusStopService.getRelationBusRoute(new BusRoute().setUseStatus(1).setBusStopId(busStop.getId()), 3);
        one.setBusRouteList(busRouteList);
        return one;
    }
}

