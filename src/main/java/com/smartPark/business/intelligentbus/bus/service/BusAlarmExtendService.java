package com.smartPark.business.intelligentbus.bus.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.intelligentbus.bus.entity.BusAlarmExtend;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * BusAlarmExtend表服务接口
 *
 * <AUTHOR>
 * @date 2023/05/13
 */
public interface BusAlarmExtendService extends IService<BusAlarmExtend> {

    /**
     * 新增
     *
     * @param trafficBusAlarmExtend 实体对象
     * @return 操作结果
     */
    boolean saveOne(BusAlarmExtend trafficBusAlarmExtend);

    /**
     * 修改单条
     *
     * @param trafficBusAlarmExtend 实体对象
     * @return 修改结果
     */
    boolean updateOne(BusAlarmExtend trafficBusAlarmExtend);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param trafficBusAlarmExtend 分页参数对象
     * @return 查询分页结果
     */
    IPage<BusAlarmExtend> selectPage(Page page, BusAlarmExtend trafficBusAlarmExtend);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    BusAlarmExtend getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param trafficBusAlarmExtend 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(BusAlarmExtend trafficBusAlarmExtend, HttpServletRequest request, HttpServletResponse response);

}

