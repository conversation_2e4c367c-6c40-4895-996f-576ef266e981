package com.smartPark.business.seat.constant;

/**
 * 座椅控制常量
 */
public interface SeatInfoControl {

    /**
     * 控制源设备
     */
    Integer SOURCE_DEVICE = 1;

    /**
     * 控制源分组
     */
    Integer SOURCE_GROUP = 2;

    /**
     * 控制源计划
     */
    Integer SOURCE_PLAN = 3;

    /**
     * 计划类型 手动
     */
    Integer TYPE_HAND = 1;

    /**
     * 计划类型 自动
     */
    Integer TYPE_AUTO = 2;

    /**
     * 设备总耗电量
     */
    String combined_active_energy = "combined_active_energy";

    /**
     * 告警类型
     */
    String alarm_type = "alarm_type";

    // DDKZQ-RS485-01-0101 totalEnergy 设备总耗电量

    // SDKZQ-RS485-01-0101 totalEnergy 设备总耗电量

    // DDKZQ-RS485-02-0102 totalEnergy 设备总耗电量

    enum GrainSizeEnum{
        /**
         * 小时
         */
        HOUR("hour",(byte) 1, "yyyy-MM-dd HH", "H:00"),

        /**
         * 天
         */
        DAY("day",(byte) 2, "yyyy-MM-dd", "M-d"),

        /**
         * 月
         */
        MONTH("month",(byte) 3, "yyyy-MM", "M");

        private String name;
        private Byte code;

        private String keyFormat;

        private String nameFormat;


        GrainSizeEnum(String name, Byte code, String keyFomart, String nameFormat) {
            this.name = name;
            this.code = code;
            this.keyFormat = keyFomart;
            this.nameFormat = nameFormat;
        }

        public static GrainSizeEnum getGrainSizeEnum(Byte dateType) {
            for (GrainSizeEnum grainSizeEnum : GrainSizeEnum.values()) {
                if (grainSizeEnum.getCode().equals(dateType)) {
                    return grainSizeEnum;
                }
            }
            return null;
        }

        public Byte getCode() {
            return code;
        }

        public String getKeyformat() {
            return keyFormat;
        }

        public String getNameFormat() {
            return nameFormat;
        }
    }
}
