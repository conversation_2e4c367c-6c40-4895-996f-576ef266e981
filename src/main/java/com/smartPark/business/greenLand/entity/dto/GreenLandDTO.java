package com.smartPark.business.greenLand.entity.dto;

import com.smartPark.business.greenLand.entity.GreenLand;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/08
 * @description 绿地详情DTO
 */
@Data
public class GreenLandDTO extends GreenLand {

  /**
   * 绿地类型名称
   */
  private String typeName;

  /**
   * 最新绿地面积：初始绿地面积＋运营记录中的绿地面积，单位：㎡
   */
  private Double newGreenLandArea;

  /**
   * 最新绿化面积：初始绿化面积＋运营记录中的绿化面积，单位：㎡
   */
  private Double newGreeningLandArea;

  /**
   * 单个绿地统计数据
   */
  private List<GreenLandCountDTO> countDTOList;
}
