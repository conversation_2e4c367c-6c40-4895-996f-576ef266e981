package com.smartPark.business.smartNetwork.entity.vo;

import com.smartPark.business.smartNetwork.entity.PipelineArchives;
import com.smartPark.common.alarm.entity.vo.AlarmVo;
import lombok.Data;

import java.util.List;

/**
 * 管道档案
 */
@Data
public class PipelineArchivesVo extends PipelineArchives {

    private String objName;

    private List<String> pipeMonitorDeviceCodeList;

    private List<PipeMonitorDeviceDTO> pipeMonitorDeviceList;

    private List<String> areaPaths;

    private List<AlarmVo> alarmList;

}
