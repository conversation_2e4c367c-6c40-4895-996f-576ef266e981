package com.smartPark.business.smartNetwork.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdArchives;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdArchivesOverView;
import com.smartPark.business.smartNetwork.entity.LicenseContent;
import com.smartPark.business.smartNetwork.entity.Pollutant;
import com.smartPark.business.smartNetwork.entity.vo.DrainageHouseholdArchivesVo;

import java.util.List;
import site.morn.rest.RestMessage;

public interface DrainageHouseholdArchivesService extends IService<DrainageHouseholdArchives> {
    boolean saveOne(DrainageHouseholdArchives drainageHouseholdArchives);

    IPage<DrainageHouseholdArchivesVo> selectPage(Page page, DrainageHouseholdArchivesVo customQueryParams);

    boolean deleteByIds(List<Long> idList);

    IPage<LicenseContent> getLicenseContentByObjId(String archivesObjId,Page page);

    IPage<Pollutant> getPollutant(String archivesObjId, Page page);

    boolean updateOne(DrainageHouseholdArchives drainageHouseholdArchives);

    DrainageHouseholdArchivesVo getArchivesInfo(Long id);

    DrainageHouseholdArchivesOverView getInstallOver(DrainageHouseholdArchivesVo drainageHouseholdArchives);

    void clearPipeObjId(String objId);

    /**
     * 根据条件查询许可证内容
     * @param archives 排水户档案
     * @return 许可证内容列表
     */
    IPage<LicenseContent> getLicenseContentByArchives(DrainageHouseholdArchives archives, Page page);

    /**
     * 根据条件查询污染物项目
     * @param pollutant 污染物项目
     * @param page 分页对象
     * @return 污染物项目列表
     */
    IPage<Pollutant> getPollutantByArchives(Pollutant pollutant, Page page);

    /**
     * 排水户档案-许可证过期事件
     * @param jsonObject 传参param字符串(创建任务时设置的参数)
     * @return 任务回调结果
     */
    RestMessage taskDraingeHouseholdArchivesExpire(JSONObject jsonObject);
}
