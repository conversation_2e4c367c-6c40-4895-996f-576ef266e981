package com.smartPark.business.smartNetwork.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.smartNetwork.entity.PipelineStandard;
import com.smartPark.business.smartNetwork.mapper.PipelineStandardMapper;
import com.smartPark.business.smartNetwork.service.PipelineStandardService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.function.Consumer;

/**
 * <p>
 * 管道分析配置标准 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class PipelineStandardServiceImpl extends ServiceImpl<PipelineStandardMapper, PipelineStandard> implements PipelineStandardService {

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;

  /**
   * 根据id编辑
   * @param pipelineStandard
   */
  @Override
  @Transactional
  public void updateOne(PipelineStandard pipelineStandard) {
    StringJoiner sj = new StringJoiner("-");
    StringJoiner coreParamSj = new StringJoiner("-");
    sj.add("管道分析配置标准保存");
    coreParamSj.add("管道分析配置标准保存" + JSONUtil.toJsonStr(pipelineStandard));

    this.saveOrUpdate(pipelineStandard,(t) -> save(t));

    LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
  }

  /**
   * @Description: 查询管道分析配置标准详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public PipelineStandard findNew() {
    //查询最新的管道分析配置标准
    PipelineStandard pipelineStandard = baseMapper.findNew();
    return pipelineStandard;
  }

  /**
   * 根据时间获取管道分析配置标准
   * @param time
   * @return
   */
  @Override
  public PipelineStandard findByTime(Date time) {
    LambdaQueryWrapper<PipelineStandard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.le(PipelineStandard::getEffectStartTime,time)
            .ge(PipelineStandard::getEffectEndTime,time);
    List<PipelineStandard> standards = baseMapper.selectList(lambdaQueryWrapper);
    return standards.get(0);
  }

  /**
   * 执行新增和更新
   * @param pipelineStandard
   * @param consumer 自定义执行
   */
  private void saveOrUpdate(PipelineStandard pipelineStandard, Consumer<PipelineStandard> consumer) {
//    /**
//     * 验证重复
//     */
//    this.checkExist(waterStandard);
    Date date = new Date();
    //查询最新的记录
    PipelineStandard oldPipelineStandard = this.findNew();

    //设置最新的开始时间和结束时间
    pipelineStandard.setEffectStartTime(date);
    pipelineStandard.setEffectEndTime(oldPipelineStandard.getEffectEndTime());
    //设置之前的结束时间
    oldPipelineStandard.setEffectEndTime(date);
    //设置基本属性
    pipelineStandard.setId(null);
    this.setBase(pipelineStandard);

    //保存旧数据
    oldPipelineStandard.setModifyId(pipelineStandard.getModifyId());
    oldPipelineStandard.setModifyTime(pipelineStandard.getModifyTime());
    updateById(oldPipelineStandard);

    //保存新数据
    consumer.accept(pipelineStandard);

  }

  /**
   * 验证重复
   */
  private void checkExist(PipelineStandard pipelineStandard) {
    QueryWrapper<PipelineStandard> queryWrapper = new QueryWrapper<>();
    //todo 设置判断重复条件
    //编辑的时候存在id
    Optional.ofNullable(pipelineStandard.getId()).ifPresent(id -> queryWrapper.ne("id_",pipelineStandard.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该管道分析配置标准已存在");
    }
  }

  /**
   * 设置基本属性
   * @param pipelineStandard
   */
  private void setBase(PipelineStandard pipelineStandard) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    pipelineStandard.setModifyTime(new Date());
    pipelineStandard.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == pipelineStandard.getId()){
      pipelineStandard.setCreatorId(userId);
      pipelineStandard.setCreateTime(new Date());
    }
  }
}
