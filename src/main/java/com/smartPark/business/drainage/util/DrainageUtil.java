package com.smartPark.business.drainage.util;

import com.smartPark.business.drainage.constant.DrainageDeviceConstant;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class DrainageUtil {
    /**
     * 根据累计降雨量列表（累计降雨量会重置） 计算列表的时间区间的真实降雨量
     * @param dataList
     * @return
     */
    public static double getActualRainfall(List<Map<String, Object>> dataList) {
        if (dataList.size()<=1) {
            return 0D;
        }
        int i=0;
        BigDecimal rainfallI;
        int j=1;
        BigDecimal rainfallJ;
        double sum = 0;
        if(dataList.get(0).get(DrainageDeviceConstant.TOTAL_RAIN) instanceof BigDecimal) {
            while (j<dataList.size()) {
                rainfallI = getTotalRainValue(dataList,i);
                rainfallJ = getTotalRainValue(dataList,j);

                if (rainfallJ.compareTo(rainfallI)>=0) {
                    j++;
                } else {
                    // 数据上报出错还是重置了，先当做数据重置
                    BigDecimal temp = getTotalRainValue(dataList,j-1);
                    sum += temp.subtract(rainfallI).doubleValue() + rainfallJ.doubleValue();
                    i = j;
                    j++;
                }
            }
            // 补尾
            if(j-i>1) {
                rainfallI = getTotalRainValue(dataList,i);
                rainfallJ = getTotalRainValue(dataList,j-1);
                sum += rainfallJ.subtract(rainfallI).doubleValue();
            }
        }
        return sum;
    }

    public static BigDecimal getTotalRainValue(List<Map<String, Object>> dataList,Integer index){
        BigDecimal bigDecimal = (BigDecimal) dataList.get(index).get(DrainageDeviceConstant.TOTAL_RAIN);
        if(null != bigDecimal){
            return bigDecimal;
        }else{
            return new BigDecimal("0");
        }
    }

    public static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime toLocalDateTime(Date time) {
        return time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
