package com.smartPark.business.drainage.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.drainage.entity.DrainageRainMonitoringStatistic;
import com.smartPark.business.drainage.entity.vo.DrainageRainMonitoringStatisticVo;

import java.util.List;

/**
 * <p>
 * 排水控制/湖渠监测 服务类
 * </p>
 *
 */
public interface DrainageRainMonitoringService extends IService<DrainageRainMonitoringStatistic> {
    /**
     * 近24小时统计
     * @param id 设备id
     * @return
     */
    List<List<DrainageRainMonitoringStatisticVo>> hourTrend();


    /**
     * 本期、同期（1-12）月统计
     * @return
     */
    List<List<DrainageRainMonitoringStatisticVo>> monthTrend();

    /**
     * 定时任务回调，对昨天一天进行统计
     * @param jsonObject
     */
    void taskStatistic(JSONObject jsonObject);
}
