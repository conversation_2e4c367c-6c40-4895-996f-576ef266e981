package com.smartPark.business.pondingMonitoring.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.pondingMonitoring.device.entity.PondingMonitoringDevice;
import com.smartPark.business.pondingMonitoring.device.entity.vo.PondingMonitoringDeviceVo;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 积水监测设备表 Mapper 接口
 * </p>

 * <AUTHOR> @since
 */
public interface PondingMonitoringDeviceMapper extends BaseMapper<PondingMonitoringDevice> {

    IPage<PondingMonitoringDeviceVo> queryListByPage(Page page, @Param("pondingMonitoringDeviceVo") PondingMonitoringDeviceVo pondingMonitoringDeviceVo);

    List<DeviceArea> getAreas();

    List<PondingMonitoringDevice> selectOnline();

}
