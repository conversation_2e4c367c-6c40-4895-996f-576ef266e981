package com.smartPark.business.airquality.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.airquality.device.entity.AirEnvironmentHourStatistics;
import com.smartPark.business.airquality.device.mapper.AirEnvironmentHourStatisticsMapper;
import com.smartPark.business.airquality.device.service.AirEnvironmentHourStatisticsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 空气环境小时统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Service
public class AirEnvironmentHourStatisticsServiceImpl extends ServiceImpl<AirEnvironmentHourStatisticsMapper, AirEnvironmentHourStatistics> implements AirEnvironmentHourStatisticsService {

}
