package com.smartPark.business.garbagehouse.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.garbagehouse.device.entity.GarbageHouseDevice;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceDeviceDTO;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceVo;
import com.smartPark.business.garbagehouse.device.excel.model.GarbageHouseDeviceExportModelDTO;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 垃圾屋表 服务类
 * </p>
 *
 * 
 *
 */
public interface GarbageHouseDeviceService extends IService<GarbageHouseDevice> {

   IPage<GarbageHouseDeviceVo> queryListByPage(RequestModel<GarbageHouseDeviceVo> requestModel);

    /**
     * 增加
     * @param garbageHouseDevice
     */
    void insert(GarbageHouseDevice garbageHouseDevice);

    /**
     * 根据id编辑
     * @param garbageHouseDevice
     */
    void updateOne(GarbageHouseDevice garbageHouseDevice);

    /**
     * @Description: 查询设备信息
     * 
     * @param flag true 验证设备码 false不验证
     * @date 
     */
    GarbageHouseDeviceDeviceDTO findDeviceByDeviceId(String deviceCode,Boolean flag);

    /**
     * @Description: 删除垃圾屋（包含批量删除）
     * 
     * @date 
     */
    void delBatch(Set<Long> ids);

  /**
   * @Description: 根据id查询垃圾屋详情
   * 
   * @date 
   */
    GarbageHouseDeviceDeviceDTO findById(Long id);

    /**
     * @Description: 根据条件导出
     * 
     * @date 
     */
    Long export(GarbageHouseDeviceVo garbageHouseDeviceVo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询导出excel数据
     * @param garbageHouseDeviceVo
     * @return
     */
    List<GarbageHouseDeviceExportModelDTO> queryList4Export(GarbageHouseDeviceVo garbageHouseDeviceVo);

    /**
     * @Description: 垃圾屋导入
     * 
     * @date 
     */
    Long imports(MultipartFile file) throws IOException;

    DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag);

    /**
     * @Description: 垃圾屋导入修改
     * 
     * @date 
     */
    Long importUpdate(MultipartFile file) throws IOException;
    /**
     * @Description: 增加垃圾屋(批量)
     * 
     * @date 
     */
    void insertBatch(Set<String> deviceCodes);
}
