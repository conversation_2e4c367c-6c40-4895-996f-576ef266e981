package com.smartPark.business.streetlight.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.smartPark.business.streetlight.entity.vo.StreetlightVo;
import com.smartPark.business.streetlight.excel.model.StreetlightExportModel;
import com.smartPark.business.streetlight.excel.model.StreetlightExportModelDTO;
import com.smartPark.business.streetlight.service.StreetlightService;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
public class StreetlightHandler extends CommonExportHandler<StreetlightExportModel> {

    @Resource
    private StreetlightService streetlightService;

    @Resource
    private RedisUtil redisUtil;



    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(StreetlightExportModel.class).build();
        context.setWriteSheet(sheet);
    }


    @Override
    public ExportPage<StreetlightExportModel> exportData(int startPage, int limit, DataExportParam param) {
        StreetlightVo streetlightVo = (StreetlightVo)param.getParam();
        List<StreetlightExportModelDTO> list = streetlightService.queryList4Export(streetlightVo);
        List<StreetlightExportModel> exportList = StreetlightExportModel.getList4Export(list);
        ExportPage<StreetlightExportModel> result = new ExportPage<>();
        result.setTotal(new Long(list.size()));
        result.setCurrent(1L);
        result.setSize(new Long(list.size()));
        result.setRecords(exportList);
        return result;
    }


}

