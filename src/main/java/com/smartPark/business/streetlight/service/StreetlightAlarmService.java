package com.smartPark.business.streetlight.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.streetlight.entity.vo.StreetlightAlarmVo;
import com.smartPark.business.streetlight.entity.vo.StreetlightVo;
import com.smartPark.common.base.model.RequestModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 井盖告警服务
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface StreetlightAlarmService {
    /**
     * @Description: 根据区域查询告警情况
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    Map<String, Object> findAlarmStatistics(StreetlightVo streetlightVo);

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR> yuan<PERSON>
     * @date 2020/11/04 11:42
     */
    IPage<StreetlightAlarmVo> queryListByPage(RequestModel<StreetlightAlarmVo> requestModel);

    /**
     * @Description: 根据设备编码查询井盖告警日志
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    List<StreetlightAlarmVo> findAlarmByDeviceCode(String deviceCode);

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    StreetlightAlarmVo findById(Long id);

    /**
     * @Description: 根据条件导出
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    Long export(StreetlightAlarmVo manholeAlarmVo, HttpServletRequest request, HttpServletResponse response);
}
