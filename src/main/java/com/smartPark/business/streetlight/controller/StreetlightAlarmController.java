package com.smartPark.business.streetlight.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.streetlight.entity.vo.StreetlightAlarmVo;
import com.smartPark.business.streetlight.service.StreetlightAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 智慧路灯/路灯告警
 * @Description 智慧路灯/路灯告警
 * <AUTHOR> yuanfeng
 * @Date 2023/3/23 17:22
 */
@RestController
@RequestMapping("streetlightAlarm")
@Api(tags = "路灯告警")
public class StreetlightAlarmController {
    @Autowired
    private StreetlightAlarmService streetlightAlarmService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<StreetlightAlarmVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<StreetlightAlarmVo> record = streetlightAlarmService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id){
        Assert.notNull(id, "id 不能为空");
        StreetlightAlarmVo streetlightAlarmVo = streetlightAlarmService.findById(id);
        return RestBuilders.successBuilder().data(streetlightAlarmVo).build();
    }

    /**
     * @Description: 根据条件导出
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("export")
    @ApiOperation("导出")
    public RestMessage export(@RequestBody StreetlightAlarmVo streetlightAlarmVo, HttpServletRequest request, HttpServletResponse response) {
        Long taskId = streetlightAlarmService.export(streetlightAlarmVo, request, response);
        return RestBuilders.successBuilder().data(taskId).build();
    }
}
