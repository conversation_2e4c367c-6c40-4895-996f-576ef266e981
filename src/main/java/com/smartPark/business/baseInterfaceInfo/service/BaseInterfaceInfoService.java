package com.smartPark.business.baseInterfaceInfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.baseInterfaceInfo.entity.BaseInterfaceInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * BaseInterfaceInfo表服务接口
 *
 * <AUTHOR>
 * @date 2023/03/16
 */
public interface BaseInterfaceInfoService extends IService<BaseInterfaceInfo> {

    /**
     * 新增
     *
     * @param baseInterfaceInfo 实体对象
     * @return 操作结果
     */
    boolean saveOne(BaseInterfaceInfo baseInterfaceInfo);

    /**
     * 修改单条
     *
     * @param baseInterfaceInfo 实体对象
     * @return 修改结果
     */
    boolean updateOne(BaseInterfaceInfo baseInterfaceInfo);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param baseInterfaceInfo 分页参数对象
     * @return 查询分页结果
     */
    IPage<BaseInterfaceInfo> selectPage(Page page, BaseInterfaceInfo baseInterfaceInfo);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    BaseInterfaceInfo getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param baseInterfaceInfo 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(BaseInterfaceInfo baseInterfaceInfo, HttpServletRequest request, HttpServletResponse response);

}

