package com.smartPark.business.pass.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.pass.entity.PassViolation;
import com.smartPark.business.pass.entity.vo.*;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 智慧畅行/违章概览
 */
public interface PassViolationOverviewService extends IService<PassViolation> {
  BasicIndexVO basicIndex(PassViolationVo passViolationVo);

  List<TrendByTimeVO> trendByTime(PassViolationVo passViolationVo);

  List<CountByNameVO> countByName(PassViolationVo passViolationVo);

  /**
   * 获取违章类型， k：名称， v:值
   *
   * @param passViolationVo 查询参数
   * @return 违章类型
   */
  LinkedHashMap<String, String> getCategory(PassViolationVo passViolationVo);
}
