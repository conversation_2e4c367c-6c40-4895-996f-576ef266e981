package com.smartPark.business.pass.service;

import com.smartPark.business.pass.entity.PassLamp;
import com.smartPark.business.pass.entity.PassTerm;
import com.smartPark.common.entity.deviceArea.DeviceAreaTree;

import java.util.List;

/**
 * <p>
 * 智慧畅行公共
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface PassCommonService {

  /**
   * @Description: 获取区域树
   * @param type -1信号灯管理,0路口自适应事件,1电警违规,2卡口测速,3礼让行人,4禁止鸣笛,5远光灯违规
   * <AUTHOR> yuan<PERSON>
   * @date 2020/11/04 11:42
   */
  List<DeviceAreaTree> getAreaTree(Integer type);

  /**
   * @Description: 获取保存期限
   * @param type 1电警违规,2卡口测速,3礼让行人,4禁止鸣笛,5远光灯违规
   * <AUTHOR> yuan<PERSON>
   * @date 2023/04/04 11:42
   */
  PassTerm getTermByType(Integer type);

  /**
   * @Description: 修改保存期限
   * <AUTHOR> yuan<PERSON>
   * @date 2023/04/04 11:42
   */
  void updateTermByType(PassTerm passTerm);
}
