package com.smartPark.business.pass.service.impl;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 红莲湖坐标(造数据用到时候删除)
 * <AUTHOR>
 * @Date 2023/12/28 14:24
 */
@Data
public class ZbDTO {
    private String x;
    private String y;

    public ZbDTO() {
    }

    public ZbDTO(String x, String y) {
        this.x = x;
        this.y = y;
    }

    public List<ZbDTO> getList(){
        List<ZbDTO> list = new ArrayList<>();
        list.add(new ZbDTO("114.61046","30.45183"));list.add(new ZbDTO("114.61025","30.45192"));list.add(new ZbDTO("114.60102","30.45439"));list.add(new ZbDTO("114.60986","30.45208"));
        list.add(new ZbDTO("114.60796","30.45283"));list.add(new ZbDTO("114.60540","30.45361"));list.add(new ZbDTO("114.60268","30.45417"));list.add(new ZbDTO("114.60524","30.45364"));
        list.add(new ZbDTO("114.60488","30.45562"));list.add(new ZbDTO("114.60492","30.45492"));list.add(new ZbDTO("114.60503","30.45334"));list.add(new ZbDTO("114.60492","30.45492"));
        list.add(new ZbDTO("114.60491","30.45101"));list.add(new ZbDTO("114.60502","30.45347"));list.add(new ZbDTO("114.59544","30.43548"));list.add(new ZbDTO("114.60229","30.44512"));
        return list;
    }
}
