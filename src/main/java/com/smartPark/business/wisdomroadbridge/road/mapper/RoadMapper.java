package com.smartPark.business.wisdomroadbridge.road.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.wisdomroadbridge.maintenance.dto.RoadObjDTO;
import com.smartPark.business.wisdomroadbridge.maintenance.entity.vo.MaintenanceFacilityVo;
import com.smartPark.business.wisdomroadbridge.road.dto.RoadStatisticsDTO;
import com.smartPark.business.wisdomroadbridge.road.entity.Road;
import com.smartPark.business.wisdomroadbridge.roadBridgeMap.entity.vo.RoadBridgeCountVo;
import com.smartPark.business.wisdomroadbridge.roadBridgeStatistical.controller.entity.RoadBridgeStaticDto;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Road表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
public interface RoadMapper extends BaseMapper<Road> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param trafficRoad 过滤参数对象
     * @return 查询分页结果
     */
    IPage<Road> selectPage(Page page, @Param("trafficRoad") Road trafficRoad);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    Road getOneById(@Param("id") Serializable id);

    /**
     * 查询道路信息以及部件信息
     * @param maintenanceFacilityVo
     * @return 道路部件信息
     */
    List<RoadObjDTO> getRoadObjList(MaintenanceFacilityVo maintenanceFacilityVo);

    /**
     * 道路信息统计
     * @param roadBridgeCountVo 道桥统计条件vo
     * @return 道桥统计信息
     */
    List<Map<String,Object>> roadCount(RoadBridgeCountVo roadBridgeCountVo);

    /**
     * 获取区域
     * @return
     */
    List<DeviceArea> getAreas();

    List<RoadBridgeStaticDto> staticsticalByGrade();

    Road getFirstRecordByObjId(String objId);

    /**
     * 道路建设状态统计
     * @param roadType 道路类型，可为空
     * @return 道路统计信息
     */
    RoadStatisticsDTO getRoadStatistics(@Param("roadType") String roadType);
}

