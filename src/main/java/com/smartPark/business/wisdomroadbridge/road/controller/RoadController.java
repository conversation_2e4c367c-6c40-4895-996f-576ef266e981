package com.smartPark.business.wisdomroadbridge.road.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.wisdomroadbridge.road.dto.RoadStatisticsDTO;
import com.smartPark.business.wisdomroadbridge.road.entity.Road;
import com.smartPark.business.wisdomroadbridge.road.service.RoadService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.utils.GaodeMapUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 道路管理
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
@Slf4j
@RestController
@RequestMapping("trafficRoad")
public class RoadController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private RoadService trafficRoadService;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<Road> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<Road> record = trafficRoadService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        record.getRecords().forEach(r->{
            //todo 如果是对外提供的接口此坐标不用转
            if (null != r.getObjInfo()){
                r.getObjInfo().setGeometry(GaodeMapUtils.geoJsonHandler(r.getObjInfo().getGeometry()));
            }
        });
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    @ApiOperation("查询单条")
    public RestMessage selectOne(@PathVariable Serializable id) {
        return RestBuilders.successBuilder((this.trafficRoadService.getOneById(id))).build();
    }

    /**
     * 新增数据
     *
     * @param trafficRoad 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "smartRoadBridges:roadManage:add", desc = "关联设施")
    public RestMessage insert(@RequestBody Road trafficRoad) {
        return RestBuilders.successBuilder().success((this.trafficRoadService.saveOne(trafficRoad))).build();
    }

    /**
     * 修改数据
     *
     * @param trafficRoad 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改单条")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "smartRoadBridges:roadManage:edit", desc = "修改设施")
    public RestMessage update(@RequestBody Road trafficRoad) {
        return RestBuilders.successBuilder().success(this.trafficRoadService.updateOne(trafficRoad)).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "smartRoadBridges:roadManage:del", desc = "删除设施")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(this.trafficRoadService.deleteByIds(idList)).build();
    }

    /**
     * 获取有效的设备信息
     *
     * @param objInfo 实体对象
     * @return 所有数据
     */
    // @PostMapping("getValidObjInfo")
    public RestMessage getValidObjInfo(@RequestBody ObjInfo objInfo) {
        List<ObjInfo> record = trafficRoadService.getValidObjInfo(objInfo);
        return RestBuilders.successBuilder(record).build();
    }
}

