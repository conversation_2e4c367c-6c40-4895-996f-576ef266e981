package com.smartPark.business.wisdomroadbridge.road.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.wisdomroadbridge.road.entity.Road;
import com.smartPark.business.wisdomroadbridge.road.mapper.RoadMapper;
import com.smartPark.business.wisdomroadbridge.road.service.RoadService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * Road表服务实现类
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
@Slf4j
@Service("trafficRoadService")
public class RoadServiceImpl extends ServiceImpl
        <RoadMapper, Road> implements RoadService {
    @Resource
    private CommonService commonService;
    @Resource
    private ObjInfoMapper objInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("，");
        for (Long id : idList) {
            Road road = baseMapper.selectById(id);
            sj.add(road.getObjId());
            // DeviceApplicationModelRef damf = getDeviceApplicationModelRef();
            // damf.setActionType(EventUtil.DELETE);
            // damf.setObjId(road.getObjId());
            // //删除关系
            // EventUtil.publishRefEvent(damf);
            removeById(id);
        }
        LogHelper.setLogInfo("",idList.toString(),null,null,"删除道路，部件码："+sj);
        return true;
    }

    @Override
    public boolean saveOne(Road trafficRoad) {
        commonService.setCreateAndModifyInfo(trafficRoad);
        validParamRequired(trafficRoad);
        validRepeat(trafficRoad);
//        validParamFormat(trafficRoad);
        //关联表
        //获取应用名，应用id
        // DeviceApplicationModelRef device = getDeviceApplicationModelRef();
        // device.setObjId(trafficRoad.getObjId());
        // //保存 关联库
        // EventUtil.publishRefEvent(device);
        handleAreaPath(trafficRoad);
        LogHelper.setLogInfo("",trafficRoad.toString(),null,null,"新增道路，部件码："+trafficRoad.getObjId());
        return save(trafficRoad);
    }

    private void handleAreaPath(Road trafficRoad) {
        String szjd = trafficRoad.getSzjd();
        String szsq = trafficRoad.getSzsq();
        String szdywg = trafficRoad.getSzdywg();
        String area_path="";
        if (!org.springframework.util.ObjectUtils.isEmpty(szjd)) {
            area_path+=szjd;
        }
        if (!org.springframework.util.ObjectUtils.isEmpty(szsq)) {
            area_path+="@"+szsq;
        }
        if (!org.springframework.util.ObjectUtils.isEmpty(szdywg)) {
            area_path+="@"+szdywg;
        }
        trafficRoad.setAreaPath(area_path);
    }

    private DeviceApplicationModelRef getDeviceApplicationModelRef() {
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.TRAFFIC);
        DeviceApplicationModelRef device = DeviceApplicationModelRef.getInstall(baseApplication);
        device.setModelId(DeviceModelConstant.ROAD);
        return device;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(Road trafficRoad) {
        Assert.notNull(trafficRoad.getId(), "id不能为空");
        commonService.setModifyInfo(trafficRoad);

        validRepeat(trafficRoad);
        validParamFormat(trafficRoad);
        LogHelper.setLogInfo("",trafficRoad.toString(),null,null,"修改道路，部件码："+trafficRoad.getObjId());
        handleAreaPath(trafficRoad);
        return updateById(trafficRoad);
    }

    @Override
    public IPage<Road> selectPage(Page page, Road trafficRoad) {
        return baseMapper.selectPage(page, trafficRoad);
    }


    @Override
    public void export(Road trafficRoad, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public Road getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(Road trafficRoad) {
        QueryWrapper<Road> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("obj_id_", trafficRoad.getObjId());
        List<Road> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("部件码已存在");
        }
        if (ObjectUtils.isEmpty(trafficRoad.getId())) {
            throw new BusinessException("部件码已存在");
        }
        if (!trafficRoad.getId().equals(list.get(0).getId())) {
            throw new BusinessException("部件码已存在");
        }

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(Road trafficRoad) {
        Assert.notNull(trafficRoad, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(trafficRoad.getObjId()), "部件码为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(Road trafficRoad) {
//        Assert.isTrue(trafficRoad.getObjId() == null || (trafficRoad.getObjId().length() == 16 && trafficRoad.getObjId().startsWith("0201", 6)), "部件码校验不通过");
        Assert.isTrue(trafficRoad.getObjId() == null || null != objInfoMapper.findByMonitorPointBsm(trafficRoad.getObjId()), "部件码不存在");
    }

    @Deprecated
    @Override
    public List<ObjInfo> getValidObjInfo(ObjInfo objInfo) {
        Road road = new Road().setObjInfo(objInfo).setObjId(objInfo.getObjId());
        validParamFormat(road);
        validRepeat(road);
        return objInfoMapper.selectList(new QueryWrapper<>(objInfo));
    }
}

