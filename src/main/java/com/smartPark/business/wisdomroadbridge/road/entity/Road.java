package com.smartPark.business.wisdomroadbridge.road.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.entity.BaseEntity;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("traffic_road")
public class Road extends BaseEntity<Road> {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO, value = "id_")
    private Long id;

    /* ------------- 开始，会复制一份放到objInfo里返给前端 ------------------ */

    /**
     * 部件标识码 : 道路编码
     */
    // 改为输入
    @TableField("obj_id_")
    private String objId;

    /**
     * 道路名称
     */
    // 改为输入
    @TableField(value = "obj_name_",updateStrategy = FieldStrategy.IGNORED)
    private String objName;

    /**
     * 所在街道
     */
    @TableField("szjd_")
    private String szjd;

    /**
     * 所在社区
     */
    @TableField("szsq_")
    private String szsq;

    /**
     * 所在单元网格
     */
    @TableField("szdywg_")
    private String szdywg;

    /**
     * 区域全路径以@拼接
     */
    @TableField("area_path_")
    private String areaPath;

    /**
     * 空间几何信息
     */
    @TableField(value ="geometry_")
    private String geometry;

    /**
     * 养护企业名称
     */
    @TableField(value ="op_enterprise_name_",  updateStrategy = FieldStrategy.IGNORED)
    private String opEnterpricseName;

    /**
     * 备注
     */
    @TableField(value = "remark_")
    private String remark;

    @TableField(exist = false)
    private ObjInfo objInfo;

    /* ------------- 结束，会复制一份放到objInfo里返给前端 ------------------ */

    /**
     * 路面结构,1-沥青路面,2-水泥混凝土路面,3-砌块路面
     */
    @TableField(value = "road_structure_", updateStrategy = FieldStrategy.IGNORED)
    private String roadStructure;

    /**
     * 道路面积
     */
    @TableField(value = "total_area_", updateStrategy = FieldStrategy.IGNORED)
    private Double totalArea;

    /**
     * 道路长度
     */
    @TableField(value = "length_", updateStrategy = FieldStrategy.IGNORED)
    private Double length;

    /**
     * 责任人
     */
    @TableField(value = "responsible_person_", updateStrategy = FieldStrategy.IGNORED)
    private String responsiblePerson;

    /**
     * 设计年限
     */
    @TableField(value = "design_year_", updateStrategy = FieldStrategy.IGNORED)
    private Double designYear;

    /**
     * 机动车道面积
     */
    @TableField(value = "motorway_area_", updateStrategy = FieldStrategy.IGNORED)
    private Double motorwayArea;

    /**
     * 起点
     */
    @TableField(value = "starting_point_", updateStrategy = FieldStrategy.IGNORED)
    private String startingPoint;

    /**
     * 巡查周期
     */
    @TableField(value = "inspection_cycle_", updateStrategy = FieldStrategy.IGNORED)
    private String inspectionCycle;

    /**
     * 道路类型 1-快速路,2-主干路,3-次干路,4-支路
     */
    @TableField(value = "road_grade_", updateStrategy = FieldStrategy.IGNORED)
    @Trans(type= TransType.DICTIONARY,key = "road_grade")
    private String roadGrade;

    /**
     * 非机动车道面积
     */
    @TableField(value = "non_motorway_area_", updateStrategy = FieldStrategy.IGNORED)
    private Double nonMotorwayArea;

    /**
     * 终点
     */
    @TableField(value = "end_point_", updateStrategy = FieldStrategy.IGNORED)
    private String endPoint;

    /**
     * 养护等级,1-小修保养/2-中修保养/3-大修保养/4-改扩建
     */
    @TableField(value = "maintenance_level_", updateStrategy = FieldStrategy.IGNORED)
    private String maintenanceLevel;

    /**
     * 建设单位
     */
    @TableField(value = "build_unit_", updateStrategy = FieldStrategy.IGNORED)
    private String buildUnit;

    /**
     * 监理单位
     */
    @TableField(value = "supervision_unit_", updateStrategy = FieldStrategy.IGNORED)
    private String supervisionUnit;

    /**
     * 开工日期
     */
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    @TableField(value = "commencement_date_", updateStrategy = FieldStrategy.IGNORED)
    private Date commencementDate;

    /**
     * 设计单位
     */
    @TableField(value = "design_unit_", updateStrategy = FieldStrategy.IGNORED)
    private String designUnit;

    /**
     * 竣工日期
     */
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    @TableField(value = "completion_date_", updateStrategy = FieldStrategy.IGNORED)
    private Date completionDate;

    /**
     * 施工单位
     */
    @TableField(value = "construction_unit_", updateStrategy = FieldStrategy.IGNORED)
    private String constructionUnit;

    /**
     * 移交日期
     */
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    @TableField(value = "transfer_date_", updateStrategy = FieldStrategy.IGNORED)
    private Date transferDate;

    /**
     * 接管日期
     */
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    @TableField(value = "takeover_date_", updateStrategy = FieldStrategy.IGNORED)
    private Date takeoverDate;

    /**
     * 修建日期
     */
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    @TableField(value = "build_date_", updateStrategy = FieldStrategy.IGNORED)
    private Date buildDate;

    /**
     * 建设状态，1已建设、2未建设、3建设中
     */
    @Trans(type= TransType.DICTIONARY,key = "traffic.construction_status_")
    @TableField(value = "construction_status_",  updateStrategy = FieldStrategy.IGNORED)
    private String constructionStatus;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 创建人
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 修改人
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 是否删除字段 0:未删除; 其他：删除
     */
    @TableLogic(value = "0")
    @TableField("deleted_")
    private Long deleted = 0L;

    /**
     * 修建日期起始时间
     */
    @TableField(exist = false)
    private Date buildStartTime;

    /**
     * 修建日期终止时间
     */
    @TableField(exist = false)
    private Date buildEndTime;

}
