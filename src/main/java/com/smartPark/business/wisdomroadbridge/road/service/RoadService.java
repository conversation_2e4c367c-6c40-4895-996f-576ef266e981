package com.smartPark.business.wisdomroadbridge.road.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.wisdomroadbridge.road.dto.RoadStatisticsDTO;
import com.smartPark.business.wisdomroadbridge.road.entity.Road;
import com.smartPark.common.entity.device.ObjInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * Road表服务接口
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
public interface RoadService extends IService<Road> {

    /**
     * 新增
     *
     * @param trafficRoad 实体对象
     * @return 操作结果
     */
    boolean saveOne(Road trafficRoad);

    /**
     * 修改单条
     *
     * @param trafficRoad 实体对象
     * @return 修改结果
     */
    boolean updateOne(Road trafficRoad);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param trafficRoad 分页参数对象
     * @return 查询分页结果
     */
    IPage<Road> selectPage(Page page, Road trafficRoad);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    Road getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param trafficRoad 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(Road trafficRoad, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查找有效的部件
     * @param objInfo 部件信息
     * @return
     */
    List<ObjInfo> getValidObjInfo(ObjInfo objInfo);

    /**
     * 道路建设状态统计
     * @param roadType 道路类型，可为空
     * @return 道路统计信息
     */
    RoadStatisticsDTO getRoadStatistics(String roadType);
}

