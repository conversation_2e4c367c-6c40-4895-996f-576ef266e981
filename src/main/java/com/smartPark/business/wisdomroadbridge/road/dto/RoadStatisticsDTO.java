package com.smartPark.business.wisdomroadbridge.road.dto;

import lombok.Data;

/**
 * 道路统计DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
public class RoadStatisticsDTO {
    
    /**
     * 已建设道路总里程，单位千米
     */
    private Double builtLength = 0.0;
    
    /**
     * 未建设道路总里程，单位千米
     */
    private Double nonBuildLength = 0.0;
    
    /**
     * 建设中道路总里程，单位千米
     */
    private Double buildingLength = 0.0;
    
    /**
     * 已建设道路数量
     */
    private Integer builtCount = 0;
    
    /**
     * 未建设道路数量
     */
    private Integer nonBuildCount = 0;
    
    /**
     * 建设中道路数量
     */
    private Integer buildingCount = 0;
}
