package com.smartPark.business.wisdomroadbridge.maintenance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.wisdomroadbridge.maintenance.dto.MaintenanceFacilityDTO;
import com.smartPark.business.wisdomroadbridge.maintenance.dto.MaintenanceInfoDTO;
import com.smartPark.business.wisdomroadbridge.maintenance.entity.MaintenanceInfo;
import com.smartPark.business.wisdomroadbridge.maintenance.entity.vo.MaintenanceFacilityVo;
import com.smartPark.business.wisdomroadbridge.roadBridgeStatistical.controller.entity.RoadBridgeStaticDto;
import com.smartPark.common.base.model.RequestModel;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MaintenanceInfo表服务接口
 *
 * <AUTHOR>
 * @date 2023/03/20
 */
public interface MaintenanceInfoService extends IService<MaintenanceInfo> {

    /**
     * 新增
     *
     * @param trafficMaintenanceInfo 实体对象
     * @return 操作结果
     */
    boolean saveOne(MaintenanceInfo trafficMaintenanceInfo);

    /**
     * 修改单条
     *
     * @param trafficMaintenanceInfo 实体对象
     * @return 修改结果
     */
    boolean updateOne(MaintenanceInfo trafficMaintenanceInfo);

    /**
     * 查询分页
     *
     * @param page                   分页对象
     * @param trafficMaintenanceInfo 分页参数对象
     * @return 查询分页结果
     */
    IPage<MaintenanceInfo> selectPage(Page page, MaintenanceInfo trafficMaintenanceInfo);


    List<RoadBridgeStaticDto> staticsticalByMonth(Integer facilityType, Date startTime, Date endTime);

    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    MaintenanceInfo getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param requestModel 过滤条件实体对象
     * @param request                请求
     * @param response               响应
     */
    RestMessage export(RequestModel<MaintenanceInfo> requestModel, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询Dto分页
     *
     * @param page            分页对象
     * @param maintenanceInfo 分页参数对象
     * @return 查询dto分页结果
     */
    IPage<MaintenanceInfoDTO> selectDtoPage(Page page, MaintenanceInfo maintenanceInfo);

    /**
     * 获取单条Dto
     *
     * @param id 主键id
     * @return 查询结果
     */
    MaintenanceInfoDTO getOneDtoById(Serializable id);

    /**
     * 设施下拉选择
     *
     * @param maintenanceFacilityVo 实体对象
     * @return 设施列表
     */
    List<MaintenanceFacilityDTO> maintenanceFacilityList(MaintenanceFacilityVo maintenanceFacilityVo);

    List<RoadBridgeStaticDto> staticsticalByFacilityType(Integer facilityType);

}

