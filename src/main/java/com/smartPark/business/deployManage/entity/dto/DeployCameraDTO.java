package com.smartPark.business.deployManage.entity.dto;

import cn.hutool.json.JSONArray;
import com.smartPark.business.deployManage.entity.DeployCamera;
import com.smartPark.business.deployManage.entity.DeployCameraGroup;
import com.smartPark.common.entity.device.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * DeployCamera实体类DTO
 *
 * <AUTHOR>
 * @date 2023/05/06
 */

@Data
@Accessors(chain = true)
public class DeployCameraDTO extends DeployCamera {
    /**
     * 设备扩展信息
     */
    private DeviceExtendInfo deviceExtendInfo;
    /**
     * 设备信息
     */
    private Device device;
    /**
     * 监测点位信息
     */
    private MonitorPoint monitorPoint;
    /**
     * 部件信息
     */
    private ObjInfo objInfo;

    /**
     * 设备物模型
     */
    private JSONArray physicModel;

    /**
     * 摄像机组
     */
    private List<DeployCameraGroup> cameraGroupList;

    /**
     * 设备属性状态信息，list形式
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;

    /**
     * 设备属性状态信息，map形式
     */
    private Map<String, Object> propMap;


    /**
     * 设备型号
     */
    private DeviceUnit deviceUnit;


}
