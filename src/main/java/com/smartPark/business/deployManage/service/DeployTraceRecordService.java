package com.smartPark.business.deployManage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.deployManage.entity.DeployTraceRecord;
import com.smartPark.business.deployManage.entity.dto.DeployTraceRecordDTO;
import com.smartPark.business.deployManage.entity.vo.DeployTraceRecordVo;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * DeployTraceRecord表服务接口
 *
 * <AUTHOR>
 * @date 2023/05/10
 */
public interface DeployTraceRecordService extends IService<DeployTraceRecord> {

    /**
     * 新增
     *
     * @param safeDeployTraceRecord 实体对象
     * @return 操作结果
     */
    boolean saveOne(DeployTraceRecord safeDeployTraceRecord);

    /**
     * 修改单条
     *
     * @param safeDeployTraceRecord 实体对象
     * @return 修改结果
     */
    boolean updateOne(DeployTraceRecord safeDeployTraceRecord);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param safeDeployTraceRecord 分页参数对象
     * @return 查询分页结果
     */
    IPage<DeployTraceRecord> selectPage(Page page, DeployTraceRecord safeDeployTraceRecord);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    DeployTraceRecord getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeDeployTraceRecord 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(DeployTraceRecord safeDeployTraceRecord, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询分页dto
     * @param page 分页对象
     * @param deployTraceRecordVo 查询分页对象
     * @return 所有数据
     */
    IPage<DeployTraceRecordDTO> selectDtoPage(Page page, DeployTraceRecordVo deployTraceRecordVo);
    /**
     * 通过主键查询单条数据
     *
     * @param deployTraceRecord 追溯记录实体
     * @return 单条数据
     */
    RestMessage selectDtoOne(DeployTraceRecord deployTraceRecord);
}

