package com.smartPark.business.deployManage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.deployManage.entity.DeployTraceRecord;
import com.smartPark.business.deployManage.entity.DeployTraceRecordPhoto;
import com.smartPark.business.deployManage.entity.dto.DeployTraceRecordDTO;
import com.smartPark.business.deployManage.entity.vo.DeployTraceRecordVo;
import com.smartPark.business.deployManage.mapper.DeployTraceRecordMapper;
import com.smartPark.business.deployManage.mapper.DeployTraceRecordPhotoMapper;
import com.smartPark.business.deployManage.service.DeployTraceRecordService;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * DeployTraceRecord表服务实现类
 *
 * <AUTHOR>
 * @date 2023/05/10
 */
@Slf4j
@Service
public class DeployTraceRecordServiceImpl extends ServiceImpl
        <DeployTraceRecordMapper, DeployTraceRecord> implements DeployTraceRecordService {
    @Resource
    private CommonService commonService;

    @Resource
    private DeployTraceRecordPhotoMapper traceRecordPhotoMapper;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(DeployTraceRecord::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(DeployTraceRecord safeDeployTraceRecord) {
        commonService.setCreateAndModifyInfo(safeDeployTraceRecord);

        validParamRequired(safeDeployTraceRecord);
        validRepeat(safeDeployTraceRecord);
        validParamFormat(safeDeployTraceRecord);
        return save(safeDeployTraceRecord);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(DeployTraceRecord safeDeployTraceRecord) {
        Assert.notNull(safeDeployTraceRecord.getId(), "id不能为空");
        commonService.setModifyInfo(safeDeployTraceRecord);

        validRepeat(safeDeployTraceRecord);
        validParamFormat(safeDeployTraceRecord);
        return updateById(safeDeployTraceRecord);
    }

    @Override
    public IPage<DeployTraceRecord> selectPage(Page page, DeployTraceRecord safeDeployTraceRecord) {
        return baseMapper.selectPage(page, safeDeployTraceRecord);
    }

    @Override
    public void export(DeployTraceRecord safeDeployTraceRecord, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public IPage<DeployTraceRecordDTO> selectDtoPage(Page page, DeployTraceRecordVo deployTraceRecordVo) {
        IPage<DeployTraceRecordDTO> ipage = baseMapper.selectDtoPage(page, deployTraceRecordVo);
        return ipage;
    }

    @Override
    public RestMessage selectDtoOne(DeployTraceRecord deployTraceRecord) {
        Assert.notNull(deployTraceRecord, "参数不能为空");
        Assert.notNull(deployTraceRecord.getId(), "id不能为空");
        DeployTraceRecord traceRecord = baseMapper.getOneById(deployTraceRecord.getId());
        Assert.notNull(traceRecord, "数据不存在");

        DeployTraceRecordDTO deployTraceRecordDTO = new DeployTraceRecordDTO();
        BeanUtil.copyProperties(traceRecord, deployTraceRecordDTO);

        //查询关联的照片
        QueryWrapper<DeployTraceRecordPhoto> traceRecordPhotoQw = new QueryWrapper<>();
        traceRecordPhotoQw.eq("trace_record_id_", deployTraceRecord.getId());
        traceRecordPhotoQw.eq("deleted_", CommonConstant.NOT_DELETE);
        traceRecordPhotoQw.orderByDesc("related_captured_time_");
        List<DeployTraceRecordPhoto> traceRecordPhotoList = traceRecordPhotoMapper.selectList(traceRecordPhotoQw);
        deployTraceRecordDTO.setPhotoList(traceRecordPhotoList);

        return RestBuilders.successBuilder().data(deployTraceRecordDTO).build();
    }

    @Override
    public DeployTraceRecord getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(DeployTraceRecord safeDeployTraceRecord) {

        /* List<DeployTraceRecord> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(DeployTraceRecord::getDeviceCode, safeDeployTraceRecord.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(safeDeployTraceRecord.getId()) || !safeDeployTraceRecord.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(DeployTraceRecord safeDeployTraceRecord) {
        //Assert.notNull(safeDeployTraceRecord, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(safeDeployTraceRecord.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DeployTraceRecord safeDeployTraceRecord) {
        //Assert.isTrue(safeDeployTraceRecord.getName() == null || safeDeployTraceRecord.getName().length() <= 50,
        //        "名称超长");
    }
}

