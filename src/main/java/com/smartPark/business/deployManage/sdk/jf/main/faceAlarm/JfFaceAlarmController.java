package com.smartPark.business.deployManage.sdk.jf.main.faceAlarm;

import com.smartPark.business.deployManage.sdk.jf.core.JfClient;
import com.smartPark.business.deployManage.sdk.jf.kafka.core.EmptyController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 尖峰API-人脸告警管理
 */
@RequestMapping("/jf/api/faceAlarms")
public class JfFaceAlarmController extends EmptyController {

    @Autowired
    private JfClient jfClient;

    /**
     * Create a face alarm
     * @param request the request body for creating a face alarm
     * @return the response of the created face alarm
     */
    @PostMapping
    public FaceAlarmCreateResponse createFaceAlarm(@RequestBody FaceAlarmCreateRequest request) {
        return jfClient.getFaceAlarmService().createFaceAlarm(request);
    }

    /**
     * Delete a face alarm by ID
     * @param alarmId the ID of the face alarm to delete
     */
    @DeleteMapping("/{alarmId}")
    public void deleteFaceAlarm(@PathVariable String alarmId) {
        jfClient.getFaceAlarmService().deleteFaceAlarm(alarmId);
    }

    /**
     * Update a face alarm by ID
     * @param alarmId the ID of the face alarm to update
     * @param request the request body for updating the face alarm
     */
    @PatchMapping("/{alarmId}")
    public void updateFaceAlarm(@PathVariable String alarmId, @RequestBody FaceAlarmUpdateRequest request) {
        jfClient.getFaceAlarmService().updateFaceAlarm(alarmId, request);
    }

    /**
     * Get details of a face alarm by ID
     * @param alarmId the ID of the face alarm to retrieve
     * @return the response of the face alarm details
     */
    @GetMapping("/{alarmId}")
    public FaceAlarmGetResponse getFaceAlarm(@PathVariable String alarmId) {
        return jfClient.getFaceAlarmService().getFaceAlarm(alarmId);
    }

    /**
     * Get a paginated list of face alarms
     * @param request the request body for querying the face alarm list
     * @return the response of the paginated face alarm list
     */
    @GetMapping("/pageList")
    public FaceAlarmPageQueryResponse getFaceAlarmPageList(FaceAlarmPageQueryRequest request) {
        return jfClient.getFaceAlarmService().getFaceAlarmPageList(request);
    }
}