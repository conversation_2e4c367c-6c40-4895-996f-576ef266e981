package com.smartPark.business.prowl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.prowl.entity.ProwlDeviceRef;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 巡逻计划巡逻点关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ProwlDeviceRefMapper extends BaseMapper<ProwlDeviceRef> {

    IPage<ProwlDeviceRef> queryListByPage(@Param("page") Page page, @Param("entity")  ProwlDeviceRef prowlDeviceRef);
}
