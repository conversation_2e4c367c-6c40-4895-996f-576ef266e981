package com.smartPark.business.autoIrrigate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.autoIrrigate.entity.IrrigateTimeStatistics;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateAnalyzeVo;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * IrrigateTimeStatistics表服务接口
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public interface IrrigateTimeStatisticsService extends IService<IrrigateTimeStatistics> {

    /**
     * 新增
     *
     * @param livableIrrigateTimeStatistics 实体对象
     * @return 操作结果
     */
    boolean saveOne(IrrigateTimeStatistics livableIrrigateTimeStatistics);

    /**
     * 修改单条
     *
     * @param livableIrrigateTimeStatistics 实体对象
     * @return 修改结果
     */
    boolean updateOne(IrrigateTimeStatistics livableIrrigateTimeStatistics);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param livableIrrigateTimeStatistics 分页参数对象
     * @return 查询分页结果
     */
    IPage<IrrigateTimeStatistics> selectPage(Page page, IrrigateTimeStatistics livableIrrigateTimeStatistics);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    IrrigateTimeStatistics getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param livableIrrigateTimeStatistics 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(IrrigateTimeStatistics livableIrrigateTimeStatistics, HttpServletRequest request, HttpServletResponse response);

    /**
     * 灌溉用时统计
     * @param jsonStr 传参paramObj（json）
     * @return 统一出参
     */
    RestMessage taskIrrigateUseTimeCount(String jsonStr);

    /**
     * 手动批量统计灌溉用时
     * @param irrigateAnalyzeVo 入参对象
     * @return 统一出参
     */
    RestMessage manualCountUseTimeBatch(IrrigateAnalyzeVo irrigateAnalyzeVo);
}

