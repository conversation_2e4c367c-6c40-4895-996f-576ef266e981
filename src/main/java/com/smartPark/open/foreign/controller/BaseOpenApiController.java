package com.smartPark.open.foreign.controller;

import com.alibaba.fastjson.JSONObject;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.open.foreign.service.SubConvertService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 对外接口统一转发
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/baseOpenApi")
public class BaseOpenApiController {

    @Autowired
    private SubConvertService subConvertService;

    /**
     * @Description: get请求
     * <AUTHOR> yuan<PERSON>
     * @date 2020/11/04 11:42
     */
    @GetMapping
    @ApiOperation("get请求")
    public RestMessage aepGet(String url,HttpServletRequest request) {
        JSONObject jsonObject = new JSONObject();

        // 获取所有请求参数并添加到jsonObject中
        Map<String, String[]> parameterMap = request.getParameterMap();
        JSONObject paramObject = new JSONObject();

        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            String[] values = entry.getValue();

            // 跳过url参数，因为它已经作为方法参数传入
            if ("url".equals(key)) {
                continue;
            }

            // 如果参数值只有一个，直接添加值；否则添加数组
            if (values.length == 1) {
                paramObject.put(key, values[0]);
            } else {
                paramObject.put(key, values);
            }
        }

        jsonObject.put("param", paramObject);
        String requestApplicationCode = request.getParameter("requestApplicationCode");
        jsonObject.put("requestApplicationCode", requestApplicationCode);

        RestMessage restEntity = subConvertService.subGet(url, jsonObject);
        return restEntity;
    }

    /**
     * @Description: post请求
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping
    @ApiOperation("post请求")
    public RestMessage aepPost(String url, @RequestBody JSONObject jsonObject) {
        RestMessage restEntity = subConvertService.subPost(url, jsonObject);

        return restEntity;
    }

    /**
     * @Description: post请求(自定义字段)
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("custom")
    @ApiOperation("post请求")
    public RestMessage aepPostCustom(String url, @RequestBody JSONObject jsonObject) {
        RestMessage restEntity = subConvertService.subPostCustom(url, jsonObject);
        //aep返回转换RestEntity返回
        return restEntity;
    }


    /**
     * @Description: put请求
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PutMapping
    @ApiOperation("put请求")
    public RestMessage aepPut(String url, @RequestBody JSONObject jsonObject) {
        RestMessage restEntity = subConvertService.subPut(url, jsonObject);
        return restEntity;
    }

    /**
     * @Description: post分页请求
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("post分页请求")
    public RestMessage aepPostPage(String url, @RequestBody RequestModel<JSONObject> restPage) {
        RestMessage restEntity = subConvertService.subPostPage(url, restPage);
        return restEntity;
    }

    /**
     * @Description: delete请求
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @DeleteMapping
    @ApiOperation("delete请求")
    public RestMessage aepDelete(String url, @RequestBody JSONObject jsonObject, HttpServletRequest request) {
        Object applicationCodeObj = request.getAttribute("applicationCode");
        if(applicationCodeObj != null){
            jsonObject.put("applicationCode", applicationCodeObj.toString());
        }
        RestMessage restEntity = subConvertService.subDelete(url, jsonObject);
        //aep返回转换RestEntity返回
        return restEntity;
    }


}
