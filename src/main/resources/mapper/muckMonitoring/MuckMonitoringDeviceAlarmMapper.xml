<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.muckMonitoring.mapper.MuckMonitoringDeviceAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.common.alarm.entity.Alarm">
        <id column="id_" property="id" />
        <id column="code_" property="code" />
        <result column="rule_engine_id_" property="ruleEngineId" />
        <result column="model_" property="model" />
        <result column="device_code_" property="deviceCode" />
        <result column="alarm_type_" property="alarmType" />
        <result column="push_status_" property="pushStatus" />
        <result column="work_no_" property="workNo" />
        <result column="status_" property="status" />
        <result column="level_" property="level" />
        <result column="content_" property="content" />
        <result column="source_json_" property="sourceJson" />
        <result column="alarm_time_" property="alarmTime" />
        <result column="alarm_data_" property="alarmData" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findAlarmList" resultType="com.smartPark.business.muckMonitoring.entity.vo.MuckMonitoringDeviceAlarmVo">
        SELECT
            a.id_ id,
            a.code_ code,
            a.device_code_ deviceCode,
            c.sbmc,
            c.bsm,
            c.obj_id objId,
            c.area_path areaPath,
            a.alarm_type_ alarmType,
            a.level_ level,
            a.push_status_ pushStatus,
            c.area_path areaPath,
            a.alarm_time_ alarmTime,
            a.work_no_ workNo,
            a.content_ content,
            a.source_json_ sourceJson
        FROM
            base_alarm a
                LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        <where>
            <if test="muckMonitoringDeviceAlarmVo.model != null and muckMonitoringDeviceAlarmVo.model != ''">
                a.model_ = #{muckMonitoringDeviceAlarmVo.model}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.id != null ">
                AND a.id_ = #{muckMonitoringDeviceAlarmVo.id}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.deviceCodeKey != null and muckMonitoringDeviceAlarmVo.deviceCodeKey != ''">
                AND a.device_code_ LIKE CONCAT('%',#{muckMonitoringDeviceAlarmVo.deviceCodeKey},'%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.deviceCode != null and muckMonitoringDeviceAlarmVo.deviceCode != ''">
                AND a.device_code_ = #{muckMonitoringDeviceAlarmVo.deviceCode}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szjd != null and muckMonitoringDeviceAlarmVo.szjd != ''">
                AND c.szjd =  #{muckMonitoringDeviceAlarmVo.szjd}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szsq != null and muckMonitoringDeviceAlarmVo.szsq != ''">
                AND c.szsq =  #{muckMonitoringDeviceAlarmVo.szsq}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szdywg != null and muckMonitoringDeviceAlarmVo.szdywg != ''">
                AND c.szdywg =  #{muckMonitoringDeviceAlarmVo.szdywg}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.areaPaths != null and muckMonitoringDeviceAlarmVo.areaPaths.size() > 0">
                AND c.area_path IN
                <foreach collection="muckMonitoringDeviceAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                    #{areaPath}
                </foreach>
            </if>
            <if test="muckMonitoringDeviceAlarmVo.areaPath != null and muckMonitoringDeviceAlarmVo.areaPath != ''">
                AND c.area_path =  #{muckMonitoringDeviceAlarmVo.areaPath}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.bsm != null and muckMonitoringDeviceAlarmVo.bsm != ''">
                AND c.bsm LIKE CONCAT('%',#{muckMonitoringDeviceAlarmVo.bsm},'%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.alarmType != null and muckMonitoringDeviceAlarmVo.alarmType != ''">
                AND a.alarm_type_ = #{muckMonitoringDeviceAlarmVo.alarmType}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.level != null ">
                AND a.level_ = #{muckMonitoringDeviceAlarmVo.level}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.pushStatus != null ">
                AND a.push_status_ = #{muckMonitoringDeviceAlarmVo.pushStatus}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.licensePlateNumber != null and muckMonitoringDeviceAlarmVo.licensePlateNumber != ''">
                AND a.source_json_ LIKE CONCAT('%"licensePlateNumber":%', #{muckMonitoringDeviceAlarmVo.licensePlateNumber}, '%"%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.startTime != null">
                <![CDATA[
                  AND a.alarm_time_ >= #{muckMonitoringDeviceAlarmVo.startTime}
               ]]>
            </if>
            <if test="muckMonitoringDeviceAlarmVo.endTime != null">
                <![CDATA[
                  AND a.alarm_time_ <= #{muckMonitoringDeviceAlarmVo.endTime}
               ]]>
            </if>
        </where>
        ORDER BY a.alarm_time_ DESC
    </select>

    <select id="findAlarmListGroupByDateAndDeviceCode" resultType="com.smartPark.business.muckMonitoring.entity.vo.MuckMonitoringDeviceAlarmVo">
        SELECT
        date_format(a.alarm_time_ , '%Y-%m-%d') alarmTime,
        a.device_code_ deviceCode,
        c.sbmc sbmc,
        d.objX,
        d.objY,
        Count(a.device_code_) AS alarmNumbers
        FROM
        base_alarm a
        LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        LEFT JOIN base_monitor_point_obj_v d ON c.dwbsm = d.obj_id
        <where>
            <if test="muckMonitoringDeviceAlarmVo.model != null and muckMonitoringDeviceAlarmVo.model != ''">
                a.model_ = #{muckMonitoringDeviceAlarmVo.model}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.id != null ">
                AND a.id_ = #{muckMonitoringDeviceAlarmVo.id}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.deviceCodeKey != null and muckMonitoringDeviceAlarmVo.deviceCodeKey != ''">
                AND a.device_code_ LIKE CONCAT('%',#{muckMonitoringDeviceAlarmVo.deviceCodeKey},'%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.deviceCode != null and muckMonitoringDeviceAlarmVo.deviceCode != ''">
                AND a.device_code_ = #{muckMonitoringDeviceAlarmVo.deviceCode}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szjd != null and muckMonitoringDeviceAlarmVo.szjd != ''">
                AND c.szjd =  #{muckMonitoringDeviceAlarmVo.szjd}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szsq != null and muckMonitoringDeviceAlarmVo.szsq != ''">
                AND c.szsq =  #{muckMonitoringDeviceAlarmVo.szsq}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.szdywg != null and muckMonitoringDeviceAlarmVo.szdywg != ''">
                AND c.szdywg =  #{muckMonitoringDeviceAlarmVo.szdywg}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.areaPaths != null and muckMonitoringDeviceAlarmVo.areaPaths.size() > 0">
                AND c.area_path IN
                <foreach collection="muckMonitoringDeviceAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                    #{areaPath}
                </foreach>
            </if>
            <if test="muckMonitoringDeviceAlarmVo.areaPath != null and muckMonitoringDeviceAlarmVo.areaPath != ''">
                AND c.area_path =  #{muckMonitoringDeviceAlarmVo.areaPath}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.bsm != null and muckMonitoringDeviceAlarmVo.bsm != ''">
                AND c.bsm LIKE CONCAT('%',#{muckMonitoringDeviceAlarmVo.bsm},'%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.alarmType != null and muckMonitoringDeviceAlarmVo.alarmType != ''">
                AND a.alarm_type_ = #{muckMonitoringDeviceAlarmVo.alarmType}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.level != null ">
                AND a.level_ = #{muckMonitoringDeviceAlarmVo.level}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.pushStatus != null ">
                AND a.push_status_ = #{muckMonitoringDeviceAlarmVo.pushStatus}
            </if>
            <if test="muckMonitoringDeviceAlarmVo.licensePlateNumber != null and muckMonitoringDeviceAlarmVo.licensePlateNumber != ''">
                AND a.source_json_ LIKE CONCAT('%"licensePlateNumber":%', #{muckMonitoringDeviceAlarmVo.licensePlateNumber}, '%"%')
            </if>
            <if test="muckMonitoringDeviceAlarmVo.startTime != null">
                <![CDATA[
                  AND a.alarm_time_ >= #{muckMonitoringDeviceAlarmVo.startTime}
               ]]>
            </if>
            <if test="muckMonitoringDeviceAlarmVo.endTime != null">
                <![CDATA[
                  AND a.alarm_time_ <= #{muckMonitoringDeviceAlarmVo.endTime}
               ]]>
            </if>
        </where>
        GROUP BY deviceCode,alarmTime
        ORDER BY alarmTime DESC
    </select>
</mapper>
