<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.common.device.mapper.BaseDeviceUnitServiceMapper">
  <resultMap id="BaseResultMap" type="com.smartPark.common.entity.device.BaseDeviceUnitService">
    <!--@mbg.generated-->
    <!--@Table base_device_unit_service-->
    <id column="id_" jdbcType="BIGINT" property="id" />
    <result column="device_unit_id" jdbcType="BIGINT" property="deviceUnitId" />
    <result column="device_version_id" jdbcType="BIGINT" property="deviceVersionId" />
    <result column="ser_code" jdbcType="VARCHAR" property="serCode" />
    <result column="ser_name" jdbcType="VARCHAR" property="serName" />
    <result column="show_" jdbcType="INTEGER" property="show" />
    <result column="version_desc" jdbcType="VARCHAR" property="versionDesc" />
    <result column="unit_" jdbcType="VARCHAR" property="unit" />
    <result column="specs_" jdbcType="VARCHAR" property="specs" />
    <result column="inputs" jdbcType="VARCHAR" property="inputs" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_, device_unit_id, device_version_id, ser_code, ser_name, show_, version_desc, 
    unit_, specs_, inputs, sort
  </sql>
</mapper>