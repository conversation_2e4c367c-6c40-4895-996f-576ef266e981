<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.wisdomroadbridge.road.mapper.RoadMapper">
    <resultMap id="BaseResultMap" type="com.smartPark.business.wisdomroadbridge.road.entity.Road">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="obj_id_" jdbcType="VARCHAR" property="objId"/>
        <result column="road_structure_" jdbcType="VARCHAR" property="roadStructure"/>
        <result column="total_area_" jdbcType="FLOAT" property="totalArea"/>
        <result column="length_" jdbcType="FLOAT" property="length"/>
        <result column="responsible_person_" jdbcType="VARCHAR" property="responsiblePerson"/>
        <result column="design_year_" jdbcType="FLOAT" property="designYear"/>
        <result column="motorway_area_" jdbcType="FLOAT" property="motorwayArea"/>
        <result column="starting_point_" jdbcType="VARCHAR" property="startingPoint"/>
        <result column="inspection_cycle_" jdbcType="VARCHAR" property="inspectionCycle"/>
        <result column="road_grade_" jdbcType="VARCHAR" property="roadGrade"/>
        <result column="non_motorway_area_" jdbcType="FLOAT" property="nonMotorwayArea"/>
        <result column="end_point_" jdbcType="VARCHAR" property="endPoint"/>
        <result column="maintenance_level_" jdbcType="VARCHAR" property="maintenanceLevel"/>
        <result column="build_unit_" jdbcType="VARCHAR" property="buildUnit"/>
        <result column="supervision_unit_" jdbcType="VARCHAR" property="supervisionUnit"/>
        <result column="commencement_date_" jdbcType="TIMESTAMP" property="commencementDate"/>
        <result column="build_date_" jdbcType="TIMESTAMP" property="buildDate"/>
        <result column="design_unit_" jdbcType="VARCHAR" property="designUnit"/>
        <result column="completion_date_" jdbcType="TIMESTAMP" property="completionDate"/>
        <result column="construction_unit_" jdbcType="VARCHAR" property="constructionUnit"/>
        <result column="transfer_date_" jdbcType="TIMESTAMP" property="transferDate"/>
        <result column="takeover_date_" jdbcType="TIMESTAMP" property="takeoverDate"/>
        <result column="obj_name_" jdbcType="VARCHAR" property="objName"/>
        <result column="construction_status_" jdbcType="VARCHAR" property="constructionStatus"/>
        <result column="geometry_" jdbcType="VARCHAR" property="geometry"/>
        <result column="op_enterprise_name_" jdbcType="VARCHAR" property="opEnterpricseName"/>
        <result column="szjd_" property="szjd"/>
        <result column="szsq_" property="szsq"/>
        <result column="szdywg_" property="szdywg"/>
        <result column="area_path_" property="areaPath"/>
        <result column="remark_" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="creator_id_" jdbcType="BIGINT" property="creatorId"/>
        <result column="modify_id_" jdbcType="BIGINT" property="modifyId"/>
        <result column="deleted_" jdbcType="BIGINT" property="deleted"/>
        <association property="objInfo" javaType="com.smartPark.common.entity.device.ObjInfo">
            <id column="obj_id" jdbcType="VARCHAR" property="objId"/>
            <result column="obj_name" property="objName"/>
            <result column="init_date" jdbcType="TIMESTAMP" property="initDate"/>
            <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate"/>
            <result column="contact_person" property="contactPerson"/>
            <result column="contact_phone" property="contactPhone"/>
            <result column="obj_state" jdbcType="INTEGER" property="objState"/>
            <result column="data_source" jdbcType="INTEGER" property="dataSource"/>
            <result column="dept_name" property="deptName"/>
            <result column="first_obj_category_name" property="firstObjCategoryName"/>
            <result column="second_obj_category_name" property="secondObjCategoryName"/>
            <result column="objX" jdbcType="DOUBLE" property="objX"/>
            <result column="objY" jdbcType="DOUBLE" property="objY"/>
            <result column="op_enterprise_name" property="opEnterpricseName"/>
            <result column="owner_enterprise_name" property="ownerEnterpriseName"/>
            <result column="szjd" property="szjd"/>
            <result column="szsq" property="szsq"/>
            <result column="szdywg" property="szdywg"/>
            <result column="area_path" property="areaPath"/>
            <result column="remark" property="remark"/>
            <result column="geometry" property="geometry"/>
        </association>
    </resultMap>
    <resultMap id="RoadObjResultMap" extends="BaseResultMap" type="com.smartPark.business.wisdomroadbridge.maintenance.dto.RoadObjDTO">
        <!-- <association property="objInfo" column="obj_id_" select="com.smartPark.common.device.mapper.ObjInfoMapper.findByMonitorPointBsm"/> -->
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *,
        obj_id_ as obj_id,
        obj_name_ as obj_name,
        construction_status_ as construction_status,
        geometry_ as geometry,
        op_enterprise_name_ as op_enterprise_name,
        szjd_ as szjd,
        szsq_ as szsq,
        szdywg_ as szdywg,
        area_path_ as area_path,
        remark_ as remark
        from traffic_road
<!--                 left join base_monitor_point_obj_v on traffic_road.obj_id_ = base_monitor_point_obj_v.obj_id-->
        <where>
            deleted_ = 0
            <if test="trafficRoad.roadGrade != null and trafficRoad.roadGrade != ''">
                and traffic_road.road_grade_ = #{trafficRoad.roadGrade}
            </if>
            <if test="trafficRoad.constructionStatus != null and trafficRoad.constructionStatus != ''">
                and traffic_road.construction_status_ = #{trafficRoad.constructionStatus}
            </if>
            <if test="trafficRoad.buildStartTime != null">
                <![CDATA[
                      AND traffic_road.build_date_ >= #{trafficRoad.buildStartTime}
                ]]>
            </if>
            <if test="trafficRoad.buildEndTime != null">
                <![CDATA[
                      AND traffic_road.build_date_ <= #{trafficRoad.buildEndTime}
                ]]>
            </if>
            <if test="trafficRoad.objInfo != null">
                <if test="trafficRoad.objInfo.objName != null and trafficRoad.objInfo.objName != ''">
                    and traffic_road.obj_name_ like concat('%', #{trafficRoad.objInfo.objName}, '%')
                </if>
                <if test="trafficRoad.objInfo.objId != null and trafficRoad.objInfo.objId != ''">
                    and traffic_road.obj_id_ like concat('%', #{trafficRoad.objInfo.objId}, '%')
                </if>
                <if test="trafficRoad.objInfo.szjd != null and trafficRoad.objInfo.szjd != ''">
                    and traffic_road.szjd_ = #{trafficRoad.objInfo.szjd}
                </if>
                <if test="trafficRoad.objInfo.szsq != null and trafficRoad.objInfo.szsq != ''">
                    and traffic_road.szsq_ = #{trafficRoad.objInfo.szsq}
                </if>
                <if test="trafficRoad.objInfo.szdywg != null and trafficRoad.objInfo.szdywg != ''">
                    and traffic_road.szdywg_ = #{trafficRoad.objInfo.szdywg}
                </if>
                <if test="trafficRoad.objInfo.areaPaths != null and trafficRoad.objInfo.areaPaths.size() > 0">
                    and traffic_road.area_path_ in
                    <foreach item="item" index="index" collection="trafficRoad.objInfo.areaPaths" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </where>
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *,
            obj_id_ as obj_id,
            obj_name_ as obj_name,
            construction_status_ as construction_status,
            geometry_ as geometry,
            op_enterprise_name_ as op_enterprise_name,
            szjd_ as szjd,
            szsq_ as szsq,
            szdywg_ as szdywg,
            area_path_ as area_path,
            remark_ as remark
        from traffic_road
        where id_ = #{id}
    </select>

    <select id="getRoadObjList" parameterType="com.smartPark.business.wisdomroadbridge.maintenance.entity.vo.MaintenanceFacilityVo" resultMap="RoadObjResultMap">
        select
            r.*
        from traffic_road r
<!--        left join base_monitor_point_obj_v o on r.obj_id_ = o.obj_id-->
        where r.deleted_ = 0
        <if test="objInfo != null">
            <if test="objInfo.objName != null and objInfo.objName != ''">
                and r.obj_name_ like concat('%', #{objInfo.objName}, '%')
            </if>
        </if>
        order by r.create_time_ desc
    </select>

    <select id="roadCount" parameterType="com.smartPark.business.wisdomroadbridge.roadBridgeMap.entity.vo.RoadBridgeCountVo"
            resultType="map">
        select count(*)        as roadTotalNum
             , sum(tr.length_) as roadTotalLength
        from traffic_road tr
<!--                 left join base_monitor_point_obj_v boi on-->
<!--            tr.obj_id_ = boi.obj_id-->
        where tr.deleted_ = 0
<!--          and boi.obj_id is not null-->
        <if test="areaPaths != null and areaPaths.size() != 0">
            and tr.area_path_ in
            <foreach collection="areaPaths" item="item" index="index" open="("
                     separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAreas" resultType="com.smartPark.common.entity.deviceArea.DeviceArea">
        SELECT distinct a.szjd_,
                        a.szsq_,
                        a.szdywg_
        FROM traffic_road a
<!--                 inner join base_monitor_point_obj_v b ON a.obj_id_ = b.obj_id-->
        WHERE a.deleted_ = 0
    </select>
    <select id="staticsticalByGrade"
            resultType="com.smartPark.business.wisdomroadbridge.roadBridgeStatistical.controller.entity.RoadBridgeStaticDto">
        select count(*)        as countSum
        , sum(tr.length_) as lengthSum,
        road_grade_ as type
        from traffic_road tr
<!--        left join base_monitor_point_obj_v boi on-->
<!--        tr.obj_id_ = boi.obj_id-->
        where tr.deleted_ = 0
<!--        and boi.obj_id is not null-->
        group by road_grade_
    </select>
    <select id="getFirstRecordByObjId" resultMap="BaseResultMap">
        select r.* from traffic_road r
        where r.obj_id_ = #{objId} order by r.create_time_ asc limit 1
    </select>
</mapper>
