<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.hazardousMonitoring.mapper.HazardousPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.hazardousMonitoring.entity.HazardousPoint">
        <id column="id_" property="id" />
        <result column="bsm_" property="bsm" />
        <result column="wxydm_" property="wxydm" />
        <result column="wxydl_" property="wxydl" />
        <result column="wxyxl_" property="wxyxl" />
        <result column="wxymc_" property="wxymc" />
        <result column="wxydz_" property="wxydz" />
        <result column="zbX" property="zbX" />
        <result column="zbY" property="zbY" />
        <result column="wxyms_" property="wxyms" />
        <result column="wxymj_" property="wxymj" />
        <result column="wxdj_" property="wxdj" />
        <result column="zgdw_" property="zgdw" />
        <result column="zgdwdz_" property="zgdwdz" />
        <result column="zgdwfzr_" property="zgdwfzr" />
        <result column="wxyyxmj_" property="wxyyxmj" />
        <result column="sjsynx_" property="sjsynx" />
        <result column="trsysj_" property="trsysj" />
        <result column="yxfw_" property="yxfw" />
        <result column="wxjb_" property="wxjb" />
        <result column="knzhxs_" property="knzhxs" />
        <result column="sjsjsj_" property="sjsjsj" />
        <result column="sjsjr_" property="sjsjr" />
        <result column="sjrlxfs_" property="sjrlxfs" />
        <result column="szjd_" property="szjd"/>
        <result column="szsq_" property="szsq"/>
        <result column="szdywg_" property="szdywg"/>
        <result column="area_path_" property="areaPath"/>
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findMaxCode" resultType="java.lang.String">
        SELECT
            MAX( a.bsm_ )
        FROM
            livable_hazardous_point a
    </select>

    <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            livable_hazardous_point a
                LEFT JOIN livable_hazardous b ON a.bsm_ = b.bsm_
                AND b.deleted_ = 0
        WHERE
            b.id_ IS NULL
        <if test="entity.wxymc != null and entity.wxymc != ''">
            AND (a.wxymc_ LIKE CONCAT('%',#{entity.wxymc},'%') or a.wxydm_ LIKE CONCAT('%',#{entity.wxymc},'%'))
        </if>
        <if test="entity.wxdj != null">
            AND a.wxdj_ = #{entity.wxdj}
        </if>
        <if test="entity.wxydl != null and entity.wxydl != ''">
            AND a.wxydl_ = #{entity.wxydl}
        </if>
        ORDER BY a.bsm_
    </select>

</mapper>
