<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.streetlight.mapper.StreetlightMaintenanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.streetlight.entity.StreetlightMaintenance">
        <id column="id_" property="id" />
        <result column="maintenance_code_" property="maintenanceCode" />
        <result column="facility_code_" property="facilityCode" />
        <result column="facility_id_" property="facilityId" />
        <result column="facility_org_" property="facilityOrg" />
        <result column="maintenance_start_time_" property="maintenanceStartTime" />
        <result column="maintenance_end_time_" property="maintenanceEndTime" />
        <result column="maintenance_head_" property="maintenanceHead" />
        <result column="maintenance_type_" property="maintenanceType" />
        <result column="maintenance_content_" property="maintenanceContent" />
        <result column="memo_" property="memo" />
        <result column="accepted_" property="accepted" />
        <result column="accept_result_" property="acceptResult" />
        <result column="accept_time_" property="acceptTime" />
        <result column="maintenance_use_" property="maintenanceUse" />
        <result column="attachment_file_url_" property="attachmentFileUrl" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            traffic_streetlight_maintenance a
        WHERE
            a.deleted_ = 0
        <if test="vo.facilityId != null">
            AND a.facility_id_ = #{vo.facilityId}
        </if>
        <if test="vo.facilityCode != null and vo.facilityCode != ''">
            AND a.facility_code_ = #{vo.facilityCode}
        </if>
        ORDER BY
            a.modify_time_ DESC
    </select>

</mapper>
