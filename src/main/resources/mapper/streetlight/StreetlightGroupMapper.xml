<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.streetlight.mapper.StreetlightGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.streetlight.entity.StreetlightGroup">
        <id column="id_" property="id" />
        <result column="name_" property="name" />
        <result column="streetlight_ids" property="streetlightIds" />
        <result column="note_" property="note" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.smartPark.business.streetlight.entity.vo.StreetlightGroupVo" extends="BaseResultMap">
        <result column="nickname" property="creatorName" />
    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT
            a.*,
            b.nickname
        FROM
            traffic_streetlight_group a
                LEFT JOIN base_user b ON a.creator_id_ = b.id
        WHERE
            a.deleted_ = 0
        <if test="vo.name != null and vo.name != ''">
            AND a.name_ LIKE CONCAT('%',#{vo.name},'%')
        </if>
        <if test="vo.ids != null and vo.ids.size() > 0">
            and a.id_ in
            <foreach collection="vo.ids" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="vo.lightIds != null and vo.lightIds.size() > 0">
            and
            <foreach collection="vo.lightIds" index="index" item="id" open="(" close=")" separator="or">
                find_in_set(#{id},a.streetlight_ids) > 0
            </foreach>
        </if>
        ORDER BY
            a.modify_time_ DESC
    </select>

</mapper>
