<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.smartNetwork.mapper.PipelineArchivesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.smartNetwork.entity.PipelineArchives">
        <id column="id_" property="id" />
        <result column="obj_id_" property="objId" />
        <result column="pipe_type_" property="pipeType" />
        <result column="starting_point_" property="startingPoint" />
        <result column="end_point_" property="endPoint" />
        <result column="pipe_category_" property="pipeCategory" />
        <result column="starting_point_elevation_" property="startingPointElevation" />
        <result column="end_point_elevation_" property="endPointElevation" />
        <result column="design_flow_" property="designFlow" />
        <result column="pipe_length_" property="pipeLength" />
        <result column="flow_sewage_plant_" property="flowSewagePlant" />
        <result column="associated_pumping_station_" property="associatedPumpingStation" />
        <result column="circuit_" property="circuit" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="creator_id_" property="creatorId" />
        <result column="modify_id_" property="modifyId" />
        <result column="deleted_" property="deleted" />
        <association property="objInfo" javaType="com.smartPark.common.entity.device.ObjInfo">
            <id column="obj_id" jdbcType="VARCHAR" property="objId"/>
            <result column="obj_name" property="objName"/>
            <result column="init_date" jdbcType="TIMESTAMP" property="initDate"/>
            <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate"/>
            <result column="contact_person" property="contactPerson"/>
            <result column="contact_phone" property="contactPhone"/>
            <result column="obj_state" jdbcType="INTEGER" property="objState"/>
            <result column="data_source" jdbcType="INTEGER" property="dataSource"/>
            <result column="dept_name" property="deptName"/>
            <result column="first_obj_category_name" property="firstObjCategoryName"/>
            <result column="second_obj_category_name" property="secondObjCategoryName"/>
            <result column="objX" jdbcType="DOUBLE" property="objX"/>
            <result column="objY" jdbcType="DOUBLE" property="objY"/>
            <result column="op_enterprise_name" property="opEnterpricseName"/>
            <result column="owner_enterprise_name" property="ownerEnterpriseName"/>
            <result column="szjd" property="szjd"/>
            <result column="szsq" property="szsq"/>
            <result column="szdywg" property="szdywg"/>
            <result column="area_path" property="areaPath"/>
            <result column="remark" property="remark"/>
        </association>
    </resultMap>
    <resultMap id="vo" type="com.smartPark.business.smartNetwork.entity.vo.PipelineArchivesVo" extends="BaseResultMap">
        <result column="obj_name" property="objName" />
    </resultMap>
    <select id="selectPage" resultMap="vo">
        select *
        from safe_pipeline_archives
        left join base_monitor_point_obj_v on safe_pipeline_archives.obj_id_ = base_monitor_point_obj_v.obj_id
        <where>
            deleted_ = 0
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.pipeType)">
                and safe_pipeline_archives.pipe_type_ = #{pipelineArchives.pipeType}
            </if>
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo)">
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.objName)">
                    and base_monitor_point_obj_v.obj_name like concat('%', #{pipelineArchives.objInfo.objName}, '%')
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.objId)">
                    and base_monitor_point_obj_v.obj_id like concat('%', #{pipelineArchives.objInfo.objId}, '%')
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szjd)">
                    and base_monitor_point_obj_v.szjd = #{pipelineArchives.objInfo.szjd}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szsq)">
                    and base_monitor_point_obj_v.szsq = #{pipelineArchives.objInfo.szsq}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szdywg)">
                    and base_monitor_point_obj_v.szdywg = #{pipelineArchives.objInfo.szdywg}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.areaPaths)">
                    and base_monitor_point_obj_v.area_path in
                    <foreach item="item" index="index" collection="pipelineArchives.objInfo.areaPaths" open="("
                             separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </where>
        order by modify_time_ desc, create_time_ desc
    </select>
    <select id="findById" resultMap="vo">
        select *
        from safe_pipeline_archives
                 left join base_monitor_point_obj_v on safe_pipeline_archives.obj_id_ = base_monitor_point_obj_v.obj_id
        <where>
            id_ = #{id}
        </where>
    </select>

    <select id="findByObjId" resultMap="vo">
        select *
        from safe_pipeline_archives
        left join base_monitor_point_obj_v on safe_pipeline_archives.obj_id_ = base_monitor_point_obj_v.obj_id
        <where>
            deleted_ = 0 and
            safe_pipeline_archives.obj_id_ = #{objId}
        </where>
    </select>

    <select id="findByCondition" resultMap="vo">
        select *
        from safe_pipeline_archives
        left join base_monitor_point_obj_v on safe_pipeline_archives.obj_id_ = base_monitor_point_obj_v.obj_id
        <where>
        deleted_ = 0
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo)">
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.objName)">
                    and base_monitor_point_obj_v.obj_name like concat('%', #{pipelineArchives.objInfo.objName}, '%')
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.objId)">
                    and base_monitor_point_obj_v.obj_id like concat('%', #{pipelineArchives.objInfo.objId}, '%')
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szjd)">
                    and base_monitor_point_obj_v.szjd = #{pipelineArchives.objInfo.szjd}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szsq)">
                    and base_monitor_point_obj_v.szsq = #{pipelineArchives.objInfo.szsq}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.szdywg)">
                    and base_monitor_point_obj_v.szdywg = #{pipelineArchives.objInfo.szdywg}
                </if>
                <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(pipelineArchives.objInfo.areaPaths)">
                    and base_monitor_point_obj_v.area_path in
                    <foreach item="item" index="index" collection="pipelineArchives.objInfo.areaPaths" open="("
                             separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </where>
    </select>


</mapper>
