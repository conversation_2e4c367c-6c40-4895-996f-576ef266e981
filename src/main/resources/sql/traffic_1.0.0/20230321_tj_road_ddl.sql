# 字段包含如下：
#
# 道路编码
# 道路名称
# 管理部门
# 权属单位
# 区域位置
# 坐标
# 部件状态
# 路面结构
# 道路总面积
# 道路长度
# 责任人
# 设计年限
# 机动车道面积
# 起点
# 巡查周期
# 道路等级
# 非机动车道面积
# 终点
# 养护等级
# 建设单位
# 监理单位
# 开工日期
# 设计单位
# 养护单位
# 竣工日期
# 施工单位
# 移交日期
# 接管日期
# 备注
# 创建时间
# 修改时间
# 创建人
# 修改人
# drop table if exists `traffic_road`;
CREATE TABLE `traffic_road`
(
    `id_`                 bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `obj_id_`             varchar(50) NOT NULL COMMENT '道路编码',
    `road_structure_`     varchar(50) COMMENT '路面结构',
    `total_area_`         float(10, 2) COMMENT '道路总面积',
    `length_`             float(10, 2) COMMENT '道路长度',
    `responsible_person_` varchar(50) COMMENT '责任人',
    `design_year_`        float(10, 2) COMMENT '设计年限',
    `motorway_area_`      float(10, 2) COMMENT '机动车道面积',
    `starting_point_`     varchar(100) COMMENT '起点',
    `inspection_cycle_`   varchar(50) COMMENT '巡查周期',
    `road_grade_`         varchar(20) COMMENT '道路等级',
    `non_motorway_area_`  float(10, 2) COMMENT '非机动车道面积',
    `end_point_`          varchar(100) COMMENT '终点',
    `maintenance_level_`  varchar(20) COMMENT '养护等级',
    `build_unit_`         varchar(100) COMMENT '建设单位',
    `supervision_unit_`   varchar(100) COMMENT '监理单位',
    `commencement_date_`  datetime COMMENT '开工日期',
    `design_unit_`        varchar(100) COMMENT '设计单位',
    `completion_date_`    datetime COMMENT '竣工日期',
    `construction_unit_`  varchar(100) COMMENT '施工单位',
    `transfer_date_`      datetime COMMENT '移交日期',
    `takeover_date_`      datetime COMMENT '接管日期',
    `create_time_`        datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time_`        datetime DEFAULT CURRENT_TIMESTAMP ON UPdate CURRENT_TIMESTAMP COMMENT '修改时间',
    `creator_id_`         bigint(20) COMMENT '创建人',
    `modify_id_`          bigint(20) COMMENT '修改人',
    `deleted_`            bigint(20)  NOT NULL COMMENT '是否删除字段 0:未删除; 其他-删除',
    PRIMARY KEY (`id_`),
    UNIQUE KEY `uk_road_code_` (`obj_id_`, `deleted_`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='道路信息表';

# drop table if exists `traffic_bridge`;
CREATE TABLE `traffic_bridge`
(
    `id_`                 bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `obj_id_`             varchar(50) NOT NULL COMMENT '桥梁编码',
    `width_`              float(10, 2) DEFAULT NULL COMMENT '桥梁总宽度',
    `maintenance_level_`  varchar(20)  DEFAULT NULL COMMENT '养护等级',
    `total_area_`         float(10, 2) DEFAULT NULL COMMENT '桥梁总面积',
    `load_`               varchar(20)  DEFAULT NULL COMMENT '桥梁荷载',
    `length_`             float(10, 2) DEFAULT NULL COMMENT '桥梁长度',
    `grade_`              varchar(20)  DEFAULT NULL COMMENT '桥梁等级',
    `road_structure_`     varchar(50)  DEFAULT NULL COMMENT '路面结构',
    `load_grade_`         varchar(20)  DEFAULT NULL COMMENT '荷载等级',
    `inspection_cycle_`   varchar(50)  DEFAULT NULL COMMENT '巡查周期',
    `starting_point_`     varchar(100) DEFAULT NULL COMMENT '起点',
    `scale_`              varchar(20)  DEFAULT NULL COMMENT '桥梁规模',
    `straddle_`           varchar(50)  DEFAULT NULL COMMENT '跨越物',
    `design_year_`        float(10, 2) DEFAULT NULL COMMENT '设计年限',
    `responsible_person_` varchar(50)  DEFAULT NULL COMMENT '责任人',
    `end_point_`          varchar(100) DEFAULT NULL COMMENT '终点',
    `build_unit_`         varchar(100) DEFAULT NULL COMMENT '建设单位',
    `supervision_unit_`   varchar(100) DEFAULT NULL COMMENT '监理单位',
    `commencement_date_`  datetime     DEFAULT NULL COMMENT '开工日期',
    `design_unit_`        varchar(100) DEFAULT NULL COMMENT '设计单位',
    `transfer_date_`      datetime     DEFAULT NULL COMMENT '移交日期',
    `construction_unit_`  varchar(100) DEFAULT NULL COMMENT '施工单位',
    `completion_date_`    datetime     DEFAULT NULL COMMENT '竣工日期',
    `takeover_date_`      datetime     DEFAULT NULL COMMENT '接管日期',
    `create_time_`        datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time_`        datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `creator_id_`         bigint(20)   DEFAULT NULL COMMENT '创建人',
    `modify_id_`          bigint(20)   DEFAULT NULL COMMENT '修改人',
    `deleted_`            bigint(20)  NOT NULL COMMENT '是否删除字段 0:未删除; 其他-删除',
    PRIMARY KEY (`id_`),
    UNIQUE KEY `uk_road_code_` (`obj_id_`, `deleted_`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='桥梁信息表';