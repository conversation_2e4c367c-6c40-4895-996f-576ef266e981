-- --------------------------
-- 停车场车位小时使用情况分布图
-- --------------------------
DROP TABLE IF EXISTS `traffic_parking_space_use_distribution`;
CREATE TABLE `traffic_parking_space_use_distribution`
(
    `id_`             bigint(20) NOT NULL AUTO_INCREMENT,
    `parking_lot_id_` bigint(20) NOT NULL COMMENT '停车场id',
    `parking_num_`    int(11) NOT NULL COMMENT '停车数量',
    `type_`           int(11) NOT NULL COMMENT '停车时长类型，0:<15m,1：15-30min 2:30-60min，3：1-1.5h,4:1.5:1-2h,6:2-3h,7:3-4h,8:4-5h,9:>5H',
    `statistic_time_` datetime NOT NULL COMMENT '统计时间',
    `create_time_`    datetime DEFAULT NULL COMMENT '创建时间',
    `modify_time_`    datetime DEFAULT NULL COMMENT '修改时间',
    `deleted_`        bigint(20) DEFAULT '0' COMMENT '是否删除 0存在,非0-删除',
    PRIMARY KEY (`id_`),
    UNIQUE KEY `uni_parking_lot_id_` (`parking_lot_id_`,`type_`,`statistic_time_`,`deleted_`)
) COMMENT='停车场车位小时使用情况分布图';