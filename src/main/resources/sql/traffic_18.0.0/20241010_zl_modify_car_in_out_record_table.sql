-- --------------------------
-- 修改出入记录表
-- --------------------------
drop table if exists traffic_car_in_out_record;
CREATE TABLE `traffic_car_in_out_record` (
 `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
 `car_no_` varchar(10) NOT NULL COMMENT '车牌号码',
 `in_time_` datetime NOT NULL COMMENT '入场时间',
 `in_photo_` varchar(500) DEFAULT NULL COMMENT '入场抓拍照片',
 `out_time_` datetime DEFAULT NULL COMMENT '出场时间',
 `out_photo_` varchar(500) DEFAULT NULL COMMENT '出场抓拍照片',
 `park_id_` varchar(10) DEFAULT NULL COMMENT '第三方系统id，关联停车场表park_id_',
 `channel_id_` varchar(10) DEFAULT NULL COMMENT '通道id',
 `channel_name_` varchar(60) DEFAULT NULL COMMENT '通道名称',
 `parking_lot_id_` bigint(20) DEFAULT NULL COMMENT '所属停车场ID',
 `parking_lot_name_` varchar(60) DEFAULT NULL COMMENT '停车场名称',
 `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
 `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
 `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
 `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
 `deleted_` bigint(20) DEFAULT '0' COMMENT '是否删除，0存在 其他-删除',
 PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='车辆进出记录表';
-- --------------------------
-- 修改费用类型字典表
-- --------------------------
UPDATE basedb.base_dictionary SET dict_code_='4' WHERE category_code_='expense_type' and dict_desc_='充值';
UPDATE basedb.base_dictionary SET dict_code_='3' WHERE category_code_='expense_type' and dict_desc_='消费';

-- -----------------------
-- 修改停车场
-- -----------------------
ALTER TABLE basedb.traffic_parking_lot ADD park_id_ varchar(100) not NULL COMMENT '停车场id,停车系统上报的parkId' AFTER exit_num_;



-- -----------------------
-- 暴露对外接口
-- -----------------------
INSERT INTO basedb.base_interface_info
(interfact_name, interface_url, application_id, application_code, controller_name, method_name, method_type, business_type, interface_state, memo, open_time, close_time, limit_rate, check_auth, check_flow, check_black_list, check_idempotent, check_safe, creator, create_time, modifier, modify_time, is_deleted)
VALUES('智慧停车-接收上报车辆出入记录', '/baseOpenApi/trafficParkingLot/inOutRecord', 2, 'traffic_service', 'ParkingLotOpenController', 'inOutRecord', 'POST', 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO basedb.base_interface_info
(interfact_name, interface_url, application_id, application_code, controller_name, method_name, method_type, business_type, interface_state, memo, open_time, close_time, limit_rate, check_auth, check_flow, check_black_list, check_idempotent, check_safe, creator, create_time, modifier, modify_time, is_deleted)
VALUES('智慧停车-接收上报收费记录', '/baseOpenApi/trafficParkingLot/billingRecord', 2, 'traffic_service', 'ParkingLotOpenController', 'billingRecord', 'POST', 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);


-- ------------------------
-- 还原traffic_billing_record数据结构
-- ------------------------
drop table if exists traffic_billing_record;
CREATE TABLE `traffic_billing_record` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT,
  `car_no_` varchar(16) NOT NULL COMMENT '车牌号码',
  `park_id_` varchar(10) DEFAULT NULL COMMENT '第三方系统id，关联停车场表park_id_',
  `parking_lot_id_` bigint(20) DEFAULT NULL COMMENT '停车场id',
  `fee_type_` varchar(16) DEFAULT NULL COMMENT '费用类型(字典code expense_type)',
  `fee_` decimal(10,2) DEFAULT NULL COMMENT '费用金额',
  `pay_time_` datetime DEFAULT NULL COMMENT '费用缴纳时间',
  `bill_no_` varchar(16) NOT NULL COMMENT '账单编号',
  `bill_time_` datetime DEFAULT NULL COMMENT '账单生成时间',
  `bill_status_` int(11) DEFAULT 1 COMMENT '账单状态 0-未缴费, 1-已缴费',
  `in_time_` datetime DEFAULT NULL COMMENT '入场时间',
  `pay_type_` varchar(16) DEFAULT NULL COMMENT '收费方式(字典code charge_type)',
  `pay_order_no_` varchar(16) DEFAULT NULL COMMENT '付款订单号',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_` bigint(20) DEFAULT '0' COMMENT '是否删除 0存在,非0-删除',
  `act_fee_` decimal(10,2) NOT NULL COMMENT '实付金额',
  `reduction_fee_` decimal(10,2) DEFAULT NULL COMMENT '减免金额',
  `pay_src_` varchar(16) DEFAULT NULL COMMENT '收费来源(字典code charge_source)',
  PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收费记录';


INSERT INTO basedb.base_interface_info
(interfact_name, interface_url, application_id, application_code, controller_name, method_name, method_type, business_type, interface_state, memo, open_time, close_time, limit_rate, check_auth, check_flow, check_black_list, check_idempotent, check_safe, creator, create_time, modifier, modify_time, is_deleted)
VALUES('智慧停车-获取停车场信息接口', '/baseOpenApi/trafficParkingLot/getParkingLotInfo', 2, 'traffic_service', 'ParkingLotOpenController', 'getParkingLotInfo', 'GET', 4, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

