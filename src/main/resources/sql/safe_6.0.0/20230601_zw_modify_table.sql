ALTER TABLE safe_house_hold_archives MODIFY COLUMN obj_id_ varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '部件标识码';

-- 排水户档案
ALTER TABLE safe_house_hold_archives ADD archives_no_ varchar(100) NULL COMMENT '排水户编号';
ALTER TABLE safe_house_hold_archives ADD archives_name_ varchar(200) NULL COMMENT '排水户名称';
ALTER TABLE safe_house_hold_archives ADD szjd_ varchar(200) NULL COMMENT '所在街道';
ALTER TABLE safe_house_hold_archives ADD szsq_ varchar(200) NULL COMMENT '所在社区';
ALTER TABLE safe_house_hold_archives ADD szdywg_ varchar(200) NULL COMMENT '所在单元网格';
ALTER TABLE safe_house_hold_archives ADD area_path_ varchar(1000) NULL COMMENT '区域全路径，以@分隔,冗余';
ALTER TABLE safe_house_hold_archives ADD contact_person_ varchar(100) NULL COMMENT '联系人';
ALTER TABLE safe_house_hold_archives ADD contact_phone_ varchar(20) NULL COMMENT '联系电话';
ALTER TABLE safe_house_hold_archives ADD points_ varchar(2000) NULL COMMENT '区域点，;隔开';

-- 排水户档案检查
ALTER TABLE safe_house_hold_check MODIFY COLUMN archives_obj_id_ varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '排水户标识码';

ALTER TABLE safe_house_hold_check ADD archives_id_ BIGINT NULL COMMENT '排水户档案id';

-- 许可证内容
ALTER TABLE safe_license_content ADD archives_id_ BIGINT NULL COMMENT '排水户档案id';

-- 污染物项目
ALTER TABLE safe_pollutant ADD archives_id_ BIGINT NULL COMMENT '排水户档案id';


