-- ----------------------------
-- 水质质量标准表
-- ----------------------------
CREATE TABLE `livable_water_standard`
(
    `id_`              bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `week_value_`      double(4,2)   DEFAULT NULL COMMENT '周达标判断',
    `month_value_`     double(4,2)   DEFAULT NULL COMMENT '月达标判断',
    `year_value_`      double(4,2)   DEFAULT NULL COMMENT '年达标判断',
    `cus_value_`       double(4,2)   DEFAULT NULL COMMENT '自定义范围判断',
    `effect_start_time_`  datetime DEFAULT NULL COMMENT '生效开始时间',
    `effect_end_time_`    datetime DEFAULT NULL COMMENT '生效结束时间',
	`creator_id_`      bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`     datetime      DEFAULT NULL COMMENT '创建时间',
	`modify_id_`       bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`     datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`          text          COMMENT  '备注',
	`deleted_`         int(1)        DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT ='水质质量标准表';
-- 默认数据
INSERT INTO `livable_water_standard`(`id_`, `week_value_`, `month_value_`, `year_value_`, `cus_value_`, `effect_start_time_`, `effect_end_time_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`, `deleted_`) VALUES (1, 0.00, 0.00, 0.00, 0.00, '1970-01-01 00:00:00', '2199-12-31 23:59:59', 1, '2023-08-15 14:08:04', 1, '2023-08-15 14:08:08', '默认数据', 0);

-- 水质监测添加平均值
ALTER TABLE livable_water_quality_grade_line_chart_detail ADD `avg_value_` varchar(32) NOT NULL COMMENT '监测项平均值';
-- 水质监测添加当前设备的监测水体等级
ALTER TABLE livable_water_quality_grade_up_to_standard ADD `device_quality_grade_` int(11) DEFAULT NULL COMMENT '设备监测水体等级(1:Ⅰ类,2:Ⅱ类,3:Ⅲ类,4:Ⅳ类,5:Ⅴ类)';
-- 设置之前的数据平均值为之前各项最值
UPDATE livable_water_quality_grade_line_chart_detail set avg_value_ = value_;
--  设置之前数据水质等级为设备等级
UPDATE livable_water_quality_grade_up_to_standard a left join livable_water_quality_device b
on a.device_code_ = b.device_code_ SET a.device_quality_grade_ = b.quality_grade_;