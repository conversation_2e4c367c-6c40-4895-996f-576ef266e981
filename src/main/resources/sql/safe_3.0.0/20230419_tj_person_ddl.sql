# drop table if exists safe_emergency_person;
create table safe_emergency_person
(
    id_          int(11)      not null auto_increment primary key,
    account_     varchar(32)  not null comment '账号,关联safe_person_big_data',
    birthday_    date         null comment '出生日期',
    id_card_     varchar(32)  null comment '身份证号码',
    health_      int(1)       null comment '健康状况 1-健康、2-良好、3-一般、4-较差',
    remark_      varchar(200) null comment '备注',
    creator_id_  bigint(20)   NULL COMMENT '创建人id',
    create_time_ datetime     NULL COMMENT '创建时间',
    modify_id_   bigint(20)   NULL COMMENT '修改人id',
    modify_time_ datetime     NULL COMMENT '修改时间',
    deleted_     int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_account_ (account_,deleted_)
) engine = InnoDB comment '应急人员';

-- 大数据平台人员表
# drop table if exists safe_person_big_data;
create table safe_person_big_data
(
    id_          int(11)     not null auto_increment primary key,
    account_     varchar(50) not null comment '人员账号',
    name_        varchar(50) not null comment '姓名',
    gender_      int(1)      not null comment '性别 0-女 1-男',
    phone_       varchar(20) not null comment '联系电话',
    email_       varchar(50) not null comment '电子邮箱',
    job_title_   varchar(50) not null comment '岗位',
    department_  varchar(50) not null comment '部门',
    creator_id_  bigint(20)  NULL COMMENT '创建人id',
    create_time_ datetime    NULL COMMENT '创建时间',
    modify_id_   bigint(20)  NULL COMMENT '修改人id',
    modify_time_ datetime    NULL COMMENT '修改时间',
    deleted_     int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_account_ (account_,deleted_)
) engine = InnoDB comment '大数据平台人员';

-- 应急队伍
# drop table if exists safe_emergency_team;
create table safe_emergency_team
(
    id_             int(11)      not null auto_increment primary key,
    team_code_      varchar(32)  not null comment '队伍编号 YJDW+8位序号',
    team_name_      varchar(50)  not null comment '队伍名称',
    department_     varchar(50)  null comment '主管部门',
    team_type_      int(1)       not null comment '队伍类型 1-日常运维 2-专项运维',
    address_        varchar(60)  null comment '地址',
    leader_account_ varchar(50)  null comment '负责人账号',
    establish_time_ date         null comment '成立时间',
    equipment_desc_ varchar(200) null comment '主要装备描述',
    specialty_desc_ varchar(200) null comment '专长描述',
    person_count_   int(11)      null comment '人员数量',
    remark_         varchar(200) null comment '备注',
    creator_id_     bigint(20)   NULL COMMENT '创建人id',
    create_time_    datetime     NULL COMMENT '创建时间',
    modify_id_      bigint(20)   NULL COMMENT '修改人id',
    modify_time_    datetime     NULL COMMENT '修改时间',
    deleted_        int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_team_code_ (team_code_,deleted_)
) engine = InnoDB comment '应急队伍';

-- 应急队伍人员关联应急队伍表
# drop table if exists safe_emergency_person_ref_team;
create table safe_emergency_person_ref_team
(
    id_        int(11) not null auto_increment primary key,
    team_id_   int(11) not null comment '队伍id',
    person_id_ int(11) not null comment '人员id',
    unique uni_team_id_person_id_ (team_id_,person_id_)
) engine = InnoDB comment '应急队伍人员关联应急队伍';

-- 应急专家组
# drop table if exists safe_emergency_expert_group;
create table safe_emergency_expert_group
(
    id_                   int(11)      not null auto_increment primary key,
    group_code_           varchar(16)  not null comment '专家组编号 YJZJ+8位序号',
    group_name_           varchar(50)  not null comment '专家组名称',
    leader_               varchar(50)  null comment '组长',
    deputy_leader_        varchar(50)  null comment '第一副组长',
    deputy_second_leader_ varchar(50)  null comment '第二副组长',
    work_content_         varchar(200) null comment '工作内容',
    member_count_         int(11)      null comment '成员数量',
    remark_               varchar(200) null comment '备注',
    creator_id_           bigint(20)   NULL COMMENT '创建人id',
    create_time_          datetime     NULL COMMENT '创建时间',
    modify_id_            bigint(20)   NULL COMMENT '修改人id',
    modify_time_          datetime     NULL COMMENT '修改时间',
    deleted_              int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_group_code_ (group_code_,deleted_)
) engine = InnoDB comment '应急专家组';

-- 应急专家
# drop table if exists safe_emergency_expert;
create table safe_emergency_expert
(
    id_                  int(11)      not null auto_increment primary key,
    expert_code_         varchar(16)  not null comment '专家编号 YJZJ+8位序号',
    name_                varchar(50)  not null comment '专家姓名',
    category_            varchar(20)  not null comment '专家类别 1-事故灾难类专家 2-公共卫生类专家 3-社会安全类专家 4-综合类专家 5-其它专家',
    gender_              int(1)       null comment '性别 0-女 1-男',
    phone_               varchar(20)  null comment '联系电话',
    job_title_           varchar(20)  null comment '职称',
    health_              int(1)       null comment '健康状况 1-健康、2-良好、3-一般、4-较差',
    birthday_            datetime     null comment '出生日期',
    id_card_             varchar(18)  null comment '身份证号码',
    address_             varchar(200) null comment '家庭住址',
    company_             varchar(100) null comment '工作单位',
    work_time_           datetime     null comment '参加工作时间',
    superior_department_ varchar(100) null comment '专家工作单位的上级主管部门',
    highest_degree_      varchar(20)  null comment '最高学历 1-博士、2-硕士、3-本科、4-大专、5-中专',
    graduate_school_     varchar(100) null comment '毕业院校',
    major_category_      varchar(100) null comment '专业类别',
    expert_group_        varchar(100) null comment '所属专家组,多个用逗号隔开',
    specialty_           varchar(200) null comment '专长描述',
    work_experience_     varchar(500) null comment '工作经历简述',
    remark_              varchar(200) null comment '备注',
    creator_id_          bigint(20)   NULL COMMENT '创建人id',
    create_time_         datetime     NULL COMMENT '创建时间',
    modify_id_           bigint(20)   NULL COMMENT '修改人id',
    modify_time_         datetime     NULL COMMENT '修改时间',
    deleted_             int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_expert_code_ (expert_code_,deleted_)
) engine = InnoDB comment '应急专家';

-- 应急专家组与专家关系表
# drop table if exists safe_emergency_expert_ref_expert_group;
create table safe_emergency_expert_ref_expert_group
(
    id_              int(11) not null auto_increment primary key,
    expert_group_id_ int(11) not null comment '应急专家组id',
    expert_id_       int(11) not null comment '应急专家id',
    unique uni_expert_group_id_expert_id_ (expert_group_id_,expert_id_)
) engine = InnoDB comment '应急专家组与专家关系表';

-- 应急物资储备库
# drop table if exists safe_emergency_supply_library;
create table safe_emergency_supply_library
(
    id_           int(11)      not null auto_increment primary key,
    library_code_ varchar(50)  not null comment '储备库编号,关联safe_supply_library_big_data',
    supply_       varchar(500) null comment '储备物资',
    remark_       varchar(200) null comment '备注',
    creator_id_   bigint(20)   NULL COMMENT '创建人id',
    create_time_  datetime     NULL COMMENT '创建时间',
    modify_id_    bigint(20)   NULL COMMENT '修改人id',
    modify_time_  datetime     NULL COMMENT '修改时间',
    deleted_      int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_library_code_ (library_code_,deleted_)
) engine = InnoDB comment '应急物资储备库';

-- 应急物资储备库大数据平台
# drop table if exists safe_supply_library_big_data;
create table safe_supply_library_big_data
(
    id_          int(11)     not null auto_increment primary key,
    obj_id_      varchar(20) not null comment '部件id',
    creator_id_  bigint(20)  NULL COMMENT '创建人id',
    create_time_ datetime    NULL COMMENT '创建时间',
    modify_id_   bigint(20)  NULL COMMENT '修改人id',
    modify_time_ datetime    NULL COMMENT '修改时间',
    deleted_     int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_obj_id_ (obj_id_,deleted_)
) engine = InnoDB comment '应急物资储备库大数据平台';

-- 应急物资
# drop table if exists safe_emergency_supply;
create table safe_emergency_supply
(
    id_            int(11)      not null auto_increment primary key,
    supply_code_   varchar(16)  not null comment '物资编号 YJWZ+8位序号',
    name_          varchar(50)  not null comment '物资名称',
    category_      varchar(20)  not null comment '类别,取数据字典',
    specification_ varchar(50)  null comment '规格型号',
    unit_          varchar(20)  null comment '计量单位',
    description_   varchar(500) null comment '物资描述',
    remark_        varchar(200) null comment '备注',
    quantity_      int(11)      null comment '物资数量',
    creator_id_    bigint(20)   NULL COMMENT '创建人id',
    create_time_   datetime     NULL COMMENT '创建时间',
    modify_id_     bigint(20)   NULL COMMENT '修改人id',
    modify_time_   datetime     NULL COMMENT '修改时间',
    deleted_       int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除',
    unique uni_supply_code_ (supply_code_,deleted_)
) engine = InnoDB comment '应急物资';

-- 应急物资与储备库关系表
# drop table if exists safe_emergency_supply_ref_library;
create table safe_emergency_supply_ref_library
(
    id_         int(11) not null auto_increment primary key,
    supply_id_  int(11) not null comment '应急物资id',
    library_id_ int(11) not null comment '应急物资储备库id',
    quantity_   int(11) not null comment '物资数量',
    unique uni_supply_id_library_id_ (supply_id_,library_id_)
) engine = InnoDB comment '应急物资与储备库关系表';

-- 出入库记录
# drop table if exists safe_emergency_supply_record;
create table safe_emergency_supply_record
(
    id_              int(11)     not null auto_increment primary key,
    supply_id_       int(11)     not null comment '应急物资id',
    library_id_      int(11)     not null comment '应急物资储备库id',
    operate_type_    int(1)      not null comment '出入库类型 1-入库 2-出库',
    quantity_        int(11)     not null comment '物资数量',
    result_quantity_ int(11)     not null comment '变化后物资数量',
    operator_name_   varchar(20) not null comment '操作人姓名',
    creator_id_      bigint(20)  NULL COMMENT '创建人id',
    create_time_     datetime    NULL COMMENT '创建时间',
    modify_id_       bigint(20)  NULL COMMENT '修改人id',
    modify_time_     datetime    NULL COMMENT '修改时间',
    deleted_         int(11) DEFAULT 0 COMMENT '是否删除 0存在,非0-删除'
) engine = InnoDB comment '出入库记录';
