-- 能源消耗 engergy consumption
-- 采集点表
CREATE TABLE `safe_collection_point`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id_`   bigint(20) DEFAULT NULL COMMENT '父id(一级父类别为0)',
    `full_id_`     varchar(64)  DEFAULT NULL COMMENT '父路径id',
    `full_name_`   varchar(500) DEFAULT NULL COMMENT '全名',
    `code_`        varchar(20)  DEFAULT NULL COMMENT '编号',
    `name_`        varchar(20)  DEFAULT NULL COMMENT '名称',
    `level_`       int(2) DEFAULT NULL COMMENT '层级',
    `order_`       int(2) DEFAULT NULL COMMENT '排序',
    `type_`        int(2) DEFAULT '1' COMMENT '类别(1.电 2.水 3.气)',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人',
    `deleted_`     int(2) DEFAULT 0 COMMENT '是否删除字段 0:未删除; 其他：删除',
    `remark_`      text COMMENT '备注/说明',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT='采集点表';
-- 分项表
CREATE TABLE `safe_subitem`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code_`        varchar(20) DEFAULT NULL COMMENT '编号',
    `name_`        varchar(20) DEFAULT NULL COMMENT '名称',
    `create_time_` datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_time_` datetime    DEFAULT NULL COMMENT '修改时间',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人',
    `deleted_`     int(2) DEFAULT 0 COMMENT '是否删除字段 0:未删除; 其他：删除',
    `remark_`      text COMMENT '备注/说明',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT='分项表';
-- 能耗分区表
CREATE TABLE `safe_energy_partition`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id_`   bigint(20) DEFAULT NULL COMMENT '父id(一级父类别为0)',
    `full_id_`     varchar(64)  DEFAULT NULL COMMENT '父路径id',
    `full_name_`   varchar(500) DEFAULT NULL COMMENT '全名',
    `code_`        varchar(20)  DEFAULT NULL COMMENT '编号',
    `name_`        varchar(20)  DEFAULT NULL COMMENT '名称',
    `level_`       int(2) DEFAULT NULL COMMENT '层级',
    `order_`       int(2) DEFAULT NULL COMMENT '排序',
    `fence_type_`  tinyint(1) DEFAULT NULL COMMENT '1:圆,2:多边形',
    `points_`      text DEFAULT NULL COMMENT '多边形点 多边形围栏坐标点 lon1,lat1;lon2,lat2;（3<=点个数<=5000）。多边形围栏外接圆半径最大为5000米。',
    `center_`      varchar(50) DEFAULT NULL COMMENT '中心点 圆形围栏中心点 longitude,latitude',
    `radius_`      int(11) DEFAULT NULL COMMENT '半径 单位米',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人',
    `deleted_`     int(2) DEFAULT 0 COMMENT '是否删除字段 0:未删除; 其他：删除',
    `remark_`      text COMMENT '备注/说明',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT='能耗分区表';
-- 能源消耗设备表（表计档案）
CREATE TABLE `safe_energy_consumption_device`
(
    `id_`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_code_`         varchar(32) NOT NULL COMMENT '物联网平台设备编码',
    `use_status_`          int(1) DEFAULT '1' COMMENT '使用状态(1启用0禁用)',
    `type_`                int(2) DEFAULT '1' COMMENT '类别(1.电 2.水 3.气)',
    `subitem_id_`          bigint(20) DEFAULT NULL COMMENT '分项id',
    `collection_point_id_` bigint(20) DEFAULT NULL COMMENT '采集点id',
    `partition_id_`        bigint(20) DEFAULT NULL COMMENT '能耗分区id',
    `creator_id_`          bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`         datetime DEFAULT NULL COMMENT '创建时间',
    `modify_id_`           bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`         datetime DEFAULT NULL COMMENT '修改时间',
    `remark_`              text COMMENT '备注',
    `deleted_`             int(1) DEFAULT '0' COMMENT '是否删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT='能源消耗设备';


-- 水 ZNSB-470AA1-14-0136 20000982
-- 电 DXDB-470AC1-01-0051 20000074
-- 气 RQB-470AA1-01-0006 20000116
INSERT INTO `base_application_model_device_unit_config` (`application_id_`, `model_id_`, `device_type_id_`, `device_unit_id_`)
VALUES
    ( 4, 22,  '20000034,20000045,20000074', '20000982,20000074,20000116');




