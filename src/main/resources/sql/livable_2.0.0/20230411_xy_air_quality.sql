-- ----------------------------
-- 空气质量
-- ----------------------------
CREATE TABLE `livable_air_quality_device`
(
    `id_`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_code_` varchar(32) NOT NULL COMMENT '物联网平台设备编码',
    `use_status_`  int(1)     DEFAULT '1' COMMENT '使用状态(1启用0禁用)',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime   DEFAULT NULL COMMENT '创建时间',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime   DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    `deleted_`     int(1)     DEFAULT '0' COMMENT '是否删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT ='空气质量设备';