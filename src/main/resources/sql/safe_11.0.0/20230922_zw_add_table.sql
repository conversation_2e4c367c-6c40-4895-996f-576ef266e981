-- 应急事件
CREATE TABLE `safe_emergency_event`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `event_code_`      varchar(100) DEFAULT NULL COMMENT '事件编号(告警编号,运营中心提供)',
    `event_type_`      varchar(50)  DEFAULT NULL COMMENT '事件类型',
    `event_level_`     int(11) DEFAULT NULL COMMENT '事件等级',
    `event_source_`    int(11) DEFAULT NULL COMMENT '事件来源,1物联监测,2人为上报',
    `execute_total_`   int(11) DEFAULT NULL COMMENT '预案总数',
    `execute_end_`     int(11) DEFAULT NULL COMMENT '完成预案数量',
    `task_total_`      int(11) DEFAULT NULL COMMENT '任务总数',
    `task_end_`        int(11) DEFAULT NULL COMMENT '完成任务数量',
    `dispose_status_`  int(11) DEFAULT NULL COMMENT '处理状态',
    `plan_start_time_` datetime     DEFAULT NULL COMMENT '计划开始时间',
    `plan_end_time_`   datetime     DEFAULT NULL COMMENT '计划完成时间',
    `actual_end_time_` datetime     DEFAULT NULL COMMENT '实际完成时间',
    `creator_id_`      bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`       bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`     datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`         int(11) DEFAULT '0' COMMENT '是否删除,0否,非0是',
    PRIMARY KEY (`id_`),
    KEY                `safe_emergency_event_event_code__IDX` (`event_code_`) USING BTREE
) ENGINE=InnoDB COMMENT='应急事件';

-- 应急事件执行人
CREATE TABLE `safe_emergency_event_executor`
(
    `id_`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `executor_id_`           bigint(20) DEFAULT NULL COMMENT '执行人id',
    `executor_name_`         varchar(200) DEFAULT NULL COMMENT '执行人名称,冗余',
    `emergency_people_type_` int(1) DEFAULT NULL COMMENT '应急执行人类型,1应急人,2应急队伍',
    `executor_team_id_`      bigint(20) DEFAULT NULL COMMENT '应急队伍id,应急队伍时非空',
    `event_task_id_`         bigint(20) DEFAULT NULL COMMENT '应急事件任务id',
    `creator_id_`            bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`           datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`             bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`           datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`               int(11) DEFAULT '0' COMMENT '是否删除,0否,非0删除',
    PRIMARY KEY (`id_`),
    KEY                      `safe_emergency_event_executor_event_task_id__IDX` (`event_task_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='应急事件预案任务执行人';


-- 应急任务处理记录
CREATE TABLE `safe_emergency_event_operate`
(
    `id_`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `event_id_`         bigint(20) DEFAULT NULL COMMENT '应急事件id',
    `event_task_id_`    bigint(20) DEFAULT NULL COMMENT '应急事件任务id',
    `operate_status_`   int(11) DEFAULT NULL COMMENT '处理状态,1处理中,2已处理',
    `operate_time_`     datetime      DEFAULT NULL COMMENT '处理时间',
    `operate_content_`  text COMMENT '处理详情',
    `file_urls_`        varchar(4000) DEFAULT NULL COMMENT '附件',
    `executor_id_`      bigint(20) DEFAULT NULL COMMENT '执行人id',
    `executor_account_` varchar(100)  DEFAULT NULL COMMENT '执行人账号(冗余应急人员的account)',
    `executor_mobile_`  varchar(100)  DEFAULT NULL COMMENT '执行人员手机号(冗余应急人员的手机号)',
    `creator_id_`       bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`      datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id_`        bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`      datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`          int(11) DEFAULT '0' COMMENT '是否删除,0否,非0删除',
    PRIMARY KEY (`id_`),
    KEY                 `safe_emergency_event_operate_event_id__IDX` (`event_id_`) USING BTREE,
    KEY                 `safe_emergency_event_operate_event_task_id__IDX` (`event_task_id_`) USING BTREE,
    KEY                 `safe_emergency_event_operate_executor_id__IDX` (`executor_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='应急事件处理记录';


-- 应急事件预案
CREATE TABLE `safe_emergency_event_plan`
(
    `id_`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `event_id_`           bigint(20) DEFAULT NULL COMMENT '应急事件id',
    `plan_code_`          varchar(100)  DEFAULT NULL COMMENT '预案编号',
    `plan_name_`          varchar(300)  DEFAULT NULL COMMENT '预案名称',
    `plan_type_`          varchar(30)   DEFAULT NULL COMMENT '预案分类,字典编码',
    `compiler_name_`      varchar(200)  DEFAULT NULL COMMENT '编制人名称',
    `compiler_unit_name_` varchar(200)  DEFAULT NULL COMMENT '编制单位名称',
    `issuer_name_`        varchar(200)  DEFAULT NULL COMMENT '签发人名称',
    `plan_content_`       text COMMENT '计划内容',
    `publish_time_`       datetime      DEFAULT NULL COMMENT '发布日期',
    `remark_`             varchar(1000) DEFAULT NULL COMMENT '备注',
    `file_urls_`          varchar(4000) DEFAULT NULL COMMENT '附件url,多个,隔开',
    `creator_id_`         bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`        datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id_`          bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`        datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`            int(11) DEFAULT '0' COMMENT '是否删除,0否,非0是',
    PRIMARY KEY (`id_`),
    KEY                   `safe_emergency_event_plan_event_id__IDX` (`event_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='应急事件预案';

-- 应急事件任务
CREATE TABLE `safe_emergency_event_task`
(
    `id_`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `event_id_`          bigint(20) DEFAULT NULL COMMENT '事件id',
    `event_plan_id_`     bigint(20) DEFAULT NULL COMMENT '事件预案id',
    `task_no_`           varchar(100)  DEFAULT NULL COMMENT '任务编号',
    `task_name_`         varchar(200)  DEFAULT NULL COMMENT '任务名称',
    `task_type_`         bigint(20) DEFAULT NULL COMMENT '任务类型',
    `task_duration_` double DEFAULT NULL COMMENT '任务执行时长,单位小时',
    `executor_type_`     int(11) DEFAULT NULL COMMENT '应急执行人类型,1应急人员,2应急队伍',
    `step_no_`           int(11) DEFAULT NULL COMMENT '任务步骤编号,从1开始',
    `task_content_`      varchar(400)  DEFAULT NULL COMMENT '任务描述',
    `szjd_`              varchar(200)  DEFAULT NULL COMMENT '所在街道',
    `szjd_code_`         varchar(50)   DEFAULT NULL COMMENT '所在街道编码',
    `szsq_`              varchar(200)  DEFAULT NULL COMMENT '所在社区',
    `szsq_code_`         varchar(100)  DEFAULT NULL COMMENT '所在社区编码',
    `szdywg_`            varchar(200)  DEFAULT NULL COMMENT '所在单元网格',
    `szdywg_code_`       varchar(100)  DEFAULT NULL COMMENT '所在单元网格编码',
    `area_path_`         varchar(600)  DEFAULT NULL COMMENT '区域全路径',
    `area_points_`       varchar(100)  DEFAULT NULL COMMENT '任务具体位置,格式[经度,维度],[]...',
    `before_task_names_` varchar(4000) DEFAULT NULL COMMENT '前置任务名称,逗号隔开',
    `before_task_ids_`   varchar(2000) DEFAULT NULL COMMENT '前置任务ids,逗号隔开',
    `remark_`            varchar(500)  DEFAULT NULL COMMENT '备注',
    `activate_status_`   int(11) DEFAULT '2' COMMENT '激活状态,1是,2否',
    `activate_time_`     datetime      DEFAULT NULL COMMENT '激活时间,流程节点激活的时间',
    `operate_status_`    int(11) DEFAULT '0' COMMENT '处理状态,0未处理(已派遣)1处理中,2已处理',
    `pass_time_`         datetime      DEFAULT NULL COMMENT '处理完结时间',
    `creator_id_`        bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`       datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id_`         bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`       datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`           int(11) DEFAULT '0' COMMENT '是否删除,0否,非0是',
    PRIMARY KEY (`id_`),
    KEY                  `safe_emergency_event_task_event_id__IDX` (`event_id_`) USING BTREE,
    KEY                  `safe_emergency_event_task_event_plan_id__IDX` (`event_plan_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='应急事件预案任务';