-- 空气质量标准
CREATE TABLE `livable_air_standard`
(
    `id_`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `point_standard_`    int(11) DEFAULT NULL COMMENT '监测点环境标准，1勾选属性，2任一属性，3所有属性',
    `park_standard_`     int(11) DEFAULT NULL COMMENT '园区环境标准，1勾选设备，2某一设备，3所有设备',
    `effect_start_time`  datetime      DEFAULT NULL COMMENT '生效开始时间',
    `effect_end_time`    datetime      DEFAULT NULL COMMENT '生效结束时间',
    `select_properties_` varchar(500)  DEFAULT NULL COMMENT '监测点标准属性，多个逗号隔开',
    `select_devices_`    varchar(2000) DEFAULT NULL COMMENT '园区标准设备，多个逗号隔开',
    `creator_id_`        bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`       datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id_`         bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`       datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`           int(11) DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    `version_`           int(11) DEFAULT NULL COMMENT '版本号，自增',
    PRIMARY KEY (`id_`),
    KEY                  `livable_air_standard_version__IDX` (`version_`) USING BTREE,
    KEY                  `livable_air_standard_effect_start_time_IDX` (`effect_start_time`) USING BTREE,
    KEY                  `livable_air_standard_effect_end_time_IDX` (`effect_end_time`) USING BTREE
) ENGINE=InnoDB COMMENT='空气质量标准';
-- 空气质量标准详情
CREATE TABLE `livable_air_standard_detail`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `standard_id_`     bigint(20) DEFAULT NULL COMMENT '标准主键',
    `properties_key_`  varchar(100) DEFAULT NULL COMMENT '属性key，余物模型对应',
    `properties_name_` varchar(300) DEFAULT NULL COMMENT '属性名称',
    `day_ave_peak_`    double(10, 2
) DEFAULT NULL COMMENT '日平均极值',
    `hour_ave_peak_`   double(10,2) DEFAULT NULL COMMENT '小时平均极值',
    `creator_id_`      bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`     datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`       bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`     datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`         int(11) DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`),
    KEY                `livable_air_standard_detail_standard_id__IDX` (`standard_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='空气质量标准详情';

-- 空气质量统计表新增字段
ALTER TABLE livable_air_environment_hour_statistics
    ADD alone_pm_25_ double(20,2) NULL COMMENT '单独PM设备PM2.5平均浓度';
ALTER TABLE livable_air_environment_hour_statistics
    ADD alone_pm_10_ double(20,2) NULL COMMENT '单独PM设备PM10平均浓度';
ALTER TABLE livable_air_environment_hour_statistics
    ADD alone_pm_flow_count_ BIGINT NULL COMMENT '单独PM设备上报次数';
ALTER TABLE livable_air_environment_hour_statistics
    ADD device_code_ varchar(100) NULL COMMENT '设备编码，统计类型为单设备时有此值';
ALTER TABLE livable_air_environment_hour_statistics
    ADD statistics_type_ INT NULL;
ALTER TABLE livable_air_environment_hour_statistics
    ADD end_reach_ INT DEFAULT 1 NULL COMMENT '整体是否达标，0否，1是';

ALTER TABLE livable_air_environment_day_statistics
    ADD alone_pm_25_ double(20,2) NULL COMMENT '单独PM设备PM2.5平均浓度';
ALTER TABLE livable_air_environment_day_statistics
    ADD alone_pm_10_ double(20,2) NULL COMMENT '单独PM设备PM10平均浓度';
ALTER TABLE livable_air_environment_day_statistics
    ADD alone_pm_flow_count_ BIGINT NULL COMMENT '单独PM设备上报次数';
ALTER TABLE livable_air_environment_day_statistics
    ADD device_code_ varchar(100) NULL COMMENT '设备编码，统计类型为单设备时有此值';
ALTER TABLE livable_air_environment_day_statistics
    ADD statistics_type_ INT NULL;
ALTER TABLE livable_air_environment_day_statistics
    ADD end_reach_ INT DEFAULT 1 NULL COMMENT '整体是否达标，0否，1是';
ALTER TABLE livable_air_environment_day_statistics
    ADD o3_ double(20,2) DEFAULT NULL COMMENT 'O3 24小时平均浓度';

-- 空气质量设备增加区域绘制字段
ALTER TABLE livable_air_quality_device
    ADD points_ varchar(2000) NULL COMMENT '区域经纬度，多个以逗号隔开';

-- 空气环境质量统计
CREATE TABLE `livable_air_environment_quality_statistics`
(
    `id_`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `standard_id_`     bigint(20) DEFAULT NULL COMMENT '配置标准id',
    `statistics_type_` int(11) DEFAULT NULL COMMENT '统计类型，1小时，2日',
    `statistics_id_`   bigint(20) DEFAULT NULL COMMENT '统计id',
    `properties_key_`  varchar(100) DEFAULT NULL COMMENT '属性key，与标准属性一直，来源物模型',
    `properties_name_` varchar(200) DEFAULT NULL COMMENT '属性名称，与标准属性一直，来源物模型',
    `ave_peak_`        double(10, 2) DEFAULT NULL COMMENT '极值',
  `reach_` int(11) DEFAULT NULL COMMENT '是否达标，0否，1是',
  `top_pollute_` int(11) DEFAULT '0' COMMENT '首要污染物,0否，1是',
  `exceeds_pollute_` int(11) DEFAULT '0' COMMENT '超标污染物,0否，1是',
  `exceeds_multiple_` double(10, 2) DEFAULT NULL COMMENT '超标倍数,（超标属性的平均浓度值-该属性的浓度限值）/该属性的浓度限值，保留两位小数',
  `device_codes_` text COMMENT '设备编码，多个逗号隔开',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_` int(11) DEFAULT '0' COMMENT '是否删除，0否，1是',
  `iaqi_` double(10,2) DEFAULT NULL COMMENT 'IAQI空气分项指数',
  PRIMARY KEY (`id_`),
  KEY `livable_air_environment_quality_statistics_standard_id__IDX` (`standard_id_`) USING BTREE,
  KEY `livable_air_environment_quality_statistics_statistics_id__IDX` (`statistics_id_`) USING BTREE
) ENGINE=InnoDB COMMENT='空气环境质量统计';

-- 刷数据
UPDATE livable_air_environment_day_statistics SET statistics_type_=1 WHERE device_code_ is null;
UPDATE livable_air_environment_hour_statistics SET statistics_type_=1 WHERE device_code_ is null;
