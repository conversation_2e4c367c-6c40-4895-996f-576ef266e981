-- 设备管理 菜单
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`)
VALUES
    ('211', '2', '设备管理', 'deviceManagement', '设备管理', 2, NULL, NULL, 211.00, 1, '/systemOperation/deviceManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21101', '211', '编辑', 'deviceManagement:edit', '编辑', 3, NULL, NULL, 211.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21102', '211', '删除', 'deviceManagement:del', '删除', 3, NULL, NULL, 211.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21103', '211', '批量导入', 'deviceManagement:import', '批量导入', 3, NULL, NULL, 211.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21104', '211', '批量导出', 'deviceManagement:export', '批量导出', 3, NULL, NULL, 211.04, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1);
-- 部件管理 菜单
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`)
VALUES
    ('212', '2', '部件管理', 'objectManagement', '部件管理', 2, NULL, NULL, 212.00, 1, '/systemOperation/objectManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21201', '212', '编辑', 'objectManagement:edit', '编辑', 3, NULL, NULL, 212.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21202', '212', '删除', 'objectManagement:del', '删除', 3, NULL, NULL, 212.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21203', '212', '批量导入', 'objectManagement:import', '批量导入', 3, NULL, NULL, 212.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21213', '212', '批量导出', 'objectManagement:export', '批量导出', 3, NULL, NULL, 212.04, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1);
-- 型号管理 菜单
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`)
VALUES
    ('213', '2', '型号管理', 'deviceUnitManagement', '型号管理', 2, NULL, NULL, 213.00, 1, '/systemOperation/deviceUnitManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21301', '213', '物模型配置', 'deviceUnitManagement:physicsModelEdit', '物模型配置', 3, NULL, NULL, 213.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21302', '213', '模块型号配置', 'deviceUnitManagement:modelDeviceUnitEdit', '模块型号配置', 3, NULL, NULL, 213.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1);
-- 告警事件管理 菜单
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`)
VALUES
    ('214', '2', '告警事件管理', 'alarmEventManagement', '告警事件管理', 2, NULL, NULL, 214.00, 1, '/systemOperation/alarmEventManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21401', '214', '新增', 'alarmEventManagement:add', '新增', 3, NULL, NULL, 214.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21402', '214', '修改', 'alarmEventManagement:edit', '修改', 3, NULL, NULL, 214.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1),
    ('21403', '214', '删除', 'alarmEventManagement:del', '删除', 3, NULL, NULL, 214.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1);