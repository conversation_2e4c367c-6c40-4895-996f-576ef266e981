DELETE FROM base_privilege WHERE id_ in ('4050301','405030101','405030102','405030103','405030105','4050302','405030201','405030202','405030203','405030204','4050303');
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4050301', '40503', '管网概览', 'smartNetwork:pipelineManagement:pipelineOverView', '管网概览', 2, NULL, NULL, 4050301.00, 1, '/smartNetwork/pipelineManagement/pipelineOverviewMap', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4050302', '40503', '管道档案', 'smartNetwork:pipelineManagement:pipelineArchives', '管道档案', 2, NULL, NULL, 4050302.00, 1, '/smartNetwork/pipelineManagement/pipelineArchives', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030201', '4050302', '新增', 'smartNetwork:pipelineManagement:pipelineArchives:add', '新增', 3, NULL, NULL, 4050302.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030202', '4050302', '编辑', 'smartNetwork:pipelineManagement:pipelineArchives:edit', '编辑', 3, NULL, NULL, 4050302.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030203', '4050302', '删除', 'smartNetwork:pipelineManagement:pipelineArchives:delete', '删除', 3, NULL, NULL, 4050302.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030205', '4050302', '查看监测数据', 'smartNetwork:pipelineManagement:pipelineArchives:view', '查看监测数据', 3, NULL, NULL, 4050302.05, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4050303', '40503', '管道监测', 'smartNetwork:pipelineManagement:pipelineMonitor', '管道监测', 2, NULL, NULL, 4050303.00, 1, '/smartNetwork/pipelineManagement/pipelineMonitor', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030301', '4050303', '新增', 'smartNetwork:pipelineManagement:pipelineMonitor:add', '新增', 3, NULL, NULL, 4050303.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030302', '4050303', '编辑', 'smartNetwork:pipelineManagement:pipelineMonitor:edit', '编辑', 3, NULL, NULL, 4050303.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030303', '4050303', '删除', 'smartNetwork:pipelineManagement:pipelineMonitor:delete', '删除', 3, NULL, NULL, 4050303.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('405030304', '4050303', '启用/停用', 'smartNetwork:pipelineManagement:pipelineMonitor:changeStatus', '启用/停用', 3, NULL, NULL, 4050303.04, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4050304', '40503', '告警管理', 'smartNetwork:pipelineManagement:pipelineAlarm', '告警管理', 2, NULL, NULL, 4050304.00, 1, '/smartNetwork/pipelineManagement/pipelineAlarm', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);

INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40101', '401', '泵站概览', 'drainageControl:pumpStationsOverView', '泵站概览', 2, NULL, NULL, 40101.00, 1, '/drainageControl/pumpStationsOverView', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40102', '401', '泵站档案', 'drainageControl:pumpStationsArchives', '泵站档案', 2, NULL, NULL, 40102.00, 1, '/drainageControl/pumpStationsArchives', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010201', '40102', '新增', 'drainageControl:pumpStationsArchives:add', '新增', 3, NULL, NULL, 40102.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010202', '40102', '编辑', 'drainageControl:pumpStationsArchives:edit', '编辑', 3, NULL, NULL, 40102.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010203', '40102', '删除', 'drainageControl:pumpStationsArchives:delete', '删除', 3, NULL, NULL, 40102.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010204', '40102', '关联', 'drainageControl:pumpStationsArchives:relate', '关联', 3, NULL, NULL, 40102.04, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40103', '401', '泵站监测', 'drainageControl:pumpStationsMonitoring', '泵站监测', 2, NULL, NULL, 40103.00, 1, '/drainageControl/pumpStationsMonitoring', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010301', '40103', '自动控制', 'drainageControl:pumpStationsMonitoring:auto', '自动控制', 3, NULL, NULL, 40103.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010302', '40103', '手动控制', 'drainageControl:pumpStationsMonitoring:manual', '手动控制', 3, NULL, NULL, 40103.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40104', '401', '泵站监控', 'drainageControl:pumpStationsMonitoringController', '泵站监控', 2, NULL, NULL, 40104.00, 1, '/drainageControl/pumpStationsMonitoringController', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40105', '401', '巡检管理', 'drainageControl:patrolManagement', '巡检管理', 2, NULL, NULL, 40105.00, 1, '/drainageControl/patrolManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010501', '40105', '新增', 'drainageControl:patrolManagement:add', '新增', 3, NULL, NULL, 40105.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010502', '40105', '编辑', 'drainageControl:patrolManagement:edit', '编辑', 3, NULL, NULL, 40105.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010503', '40105', '删除', 'drainageControl:patrolManagement:delete', '删除', 3, NULL, NULL, 40105.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40106', '401', '检修管理', 'drainageControl:maintenanceManagement', '检修管理', 2, NULL, NULL, 40106.00, 1, '/drainageControl/maintenanceManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010601', '40106', '新增', 'drainageControl:maintenanceManagement:add', '新增', 3, NULL, NULL, 40106.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010602', '40106', '编辑', 'drainageControl:maintenanceManagement:edit', '编辑', 3, NULL, NULL, 40106.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010603', '40106', '删除', 'drainageControl:maintenanceManagement:delete', '删除', 3, NULL, NULL, 40106.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40107', '401', '设备管理', 'drainageControl:equipmentManagement', '设备管理', 2, NULL, NULL, 40107.00, 1, '/drainageControl/equipmentManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010701', '40107', '关联设备', 'drainageControl:equipmentManagement:add', '关联设备', 3, NULL, NULL, 40107.01, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010702', '40107', '编辑', 'drainageControl:equipmentManagement:edit', '编辑', 3, NULL, NULL, 40107.02, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010703', '40107', '删除', 'drainageControl:equipmentManagement:delete', '删除', 3, NULL, NULL, 40107.03, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('4010704', '40107', '启用/停用', 'drainageControl:equipmentManagement:stop', '启用/停用', 3, NULL, NULL, 40107.04, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);
INSERT INTO `base_privilege`(`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`, `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`, `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`, `icon_name`, `flag_`, `application_id_`, `access_type_`) VALUES ('40108', '401', '告警管理', 'drainageControl:alarmManagement', '告警管理', 2, NULL, NULL, 40108.00, 1, '/drainageControl/alarmManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1);

-- -------------
-- 新增二级类型字段
-- -------------
alter table `base_application_model_device_unit_config` Add column `device_second_type_name_` varchar(255)  DEFAULT NULL COMMENT '大数据平台二级类型集合，兼容部分不是物联网平台设备' AFTER `model_id_`;