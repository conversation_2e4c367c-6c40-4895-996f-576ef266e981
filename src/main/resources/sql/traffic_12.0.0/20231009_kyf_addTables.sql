-- 内部车辆增加类别黑名单
ALTER TABLE traffic_internal_car MODIFY `car_type_` int(1) NOT NULL COMMENT '车辆类型 1-内部车辆 2-临时车辆 3-黑名单车辆';
-- 内部车辆增加类别黑名单
ALTER TABLE traffic_internal_car MODIFY `applicant_name_` varchar(60) DEFAULT NULL COMMENT '申请人姓名/联系人姓名';
ALTER TABLE traffic_internal_car MODIFY `phone_` varchar(60) DEFAULT NULL COMMENT '联系电话';
-- 删除是否车主
ALTER TABLE traffic_internal_car DROP COLUMN `belong_owner_`;
-- 删除品牌型号
ALTER TABLE traffic_internal_car DROP COLUMN `brand_`;
-- 删除唯一索引
ALTER TABLE traffic_internal_car DROP INDEX uk_car_no_;

-- ----------------------------
-- 收费管理sql
-- ----------------------------
-- 费用类型
ALTER TABLE traffic_billing_record MODIFY `fee_type_` varchar(16) DEFAULT NULL COMMENT '费用类型(字典code expense_type)';
-- 收费方式
ALTER TABLE traffic_billing_record MODIFY `pay_type_` varchar(16) DEFAULT NULL COMMENT '收费方式(字典code charge_type)';
-- 增加
ALTER TABLE traffic_billing_record ADD `act_fee_` decimal(10,2) NOT NULL COMMENT '实付金额';
ALTER TABLE traffic_billing_record ADD `reduction_fee_` decimal(10,2) DEFAULT NULL COMMENT '减免金额';
ALTER TABLE traffic_billing_record ADD `pay_src_` varchar(16) DEFAULT NULL COMMENT '收费来源(字典code charge_source)';

-- ----------------------------
-- 字典sql
-- ----------------------------
-- 费用类型
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '0', '临停收费', 'expense_type', '费用类型', '费用类型', 1.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1', '月卡续费', 'expense_type', '费用类型', '费用类型', 2.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '2', '消费', 'expense_type', '费用类型', '费用类型', 3.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '3', '充值', 'expense_type', '费用类型', '费用类型', 4.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);

-- 收费方式
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '0', '岗亭收费', 'charge_type', '收费方式', '收费方式', 1.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1', '票箱收费', 'charge_type', '收费方式', '收费方式', 2.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '2', '自助缴费机', 'charge_type', '收费方式', '收费方式', 3.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '3', '手持机', 'charge_type', '收费方式', '收费方式', 4.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '4', '手机APP', 'charge_type', '收费方式', '收费方式', 5.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '11', '接口收费', 'charge_type', '收费方式', '收费方式', 6.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1000', '中央缴费', 'charge_type', '收费方式', '收费方式', 7.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1007', '机器人', 'charge_type', '收费方式', '收费方式', 8.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1008', '查询机', 'charge_type', '收费方式', '收费方式', 9.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);

-- 收费来源
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '0', '现金', 'charge_source', '收费来源', '收费来源', 1.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '1', '电子账户', 'charge_source', '收费来源', '收费来源', 2.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '2', '支付宝支付', 'charge_source', '收费来源', '收费来源', 3.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '3', '微信支付', 'charge_source', '收费来源', '收费来源', 4.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
INSERT INTO `base_dictionary`(`dict_code_`, `dict_desc_`, `category_code_`, `category_name_`, `category_desc_`, `sort_no_`, `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`, `deleted_`) VALUES ( '4', '其他', 'charge_source', '收费来源', '收费来源', 5.00, '2023-04-13 15:23:03', '2023-04-13 15:23:03', 10726, 10726, 0);
-- ----------------------------
-- 违停记录表
-- ----------------------------
CREATE TABLE `traffic_parking_illegal_record`
(
    `id_`              bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code_`            varchar(32)   DEFAULT NULL COMMENT '违停编号',
    `device_code_`     varchar(32)   DEFAULT NULL COMMENT '设备编号',
    `car_no_`          varchar(10)   DEFAULT NULL COMMENT '车牌号码',
    `record_time_`     datetime      DEFAULT NULL COMMENT '违停时间',
    `target_score_`    double(4,4)   DEFAULT NULL COMMENT '置信度',
    `image_`           varchar(2000)   DEFAULT NULL COMMENT '违停图片url(多个逗号分隔)',
	`creator_id_`      bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`     datetime      DEFAULT NULL COMMENT '创建时间',
	`modify_id_`       bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`     datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`          text          COMMENT  '备注',
	`deleted_`         int(1)        DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT ='违停记录表';

-- ----------------------------
-- 智慧停车定时统计车位使用率
-- ----------------------------
INSERT INTO `base_job_entity` (
    `job_name`,
    `job_group`,
    `job_status`,
    `cron_expression`,
    `remark`,
    `job_task_type`,
    `params`,
    `job_type`,
    `want_now_run`,
    `start_date`,
    `end_date`,
    `job_class_name`,
    `create_time`,
    `modify_time`,
    `deleted_`,
    `function_path`,
    `application_id`
)
VALUES
    (
        '智慧停车定时统计车位使用率触发',
        '智慧停车统计车位使用分组',
        1,
        '0 0 0/1 * * ?',
        NULL,
        1,
        '{}',
        0,
        0,
        NULL,
        NULL,
        'com.smartPark.common.job.service.impl.JobExecutorServiceImpl',
        '2023-04-12 09:54:54',
        NULL,
        0,
        '/openapi/task/callback/parkingRate',
        2
    );
